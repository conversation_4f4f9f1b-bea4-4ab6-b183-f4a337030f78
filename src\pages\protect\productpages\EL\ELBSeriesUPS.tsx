import React, { useState, useEffect } from 'react';
import { ChevronRight, Info, Check, ArrowUpRight, Award, Zap, Shield, Clock, ArrowRight, FileText } from 'lucide-react';
import PageLayout from '@/components/layout/PageLayout';
import { motion } from 'framer-motion';

// Enhanced mobile table styles - same design as web but mobile optimized
const customStyles = `
  .specs-table-container {
    position: relative;
    width: 100%;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .specs-table-scroll {
    overflow-x: auto;
    overflow-y: visible;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: #3b82f6 #f1f5f9;
    position: relative;
  }

  .specs-table-scroll::-webkit-scrollbar {
    height: 8px;
  }

  .specs-table-scroll::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
  }

  .specs-table-scroll::-webkit-scrollbar-thumb {
    background: #3b82f6;
    border-radius: 4px;
  }

  .specs-table-scroll::-webkit-scrollbar-thumb:hover {
    background: #2563eb;
  }

  .specs-table {
    width: 100%;
    min-width: 800px;
    border-collapse: separate;
    border-spacing: 0;
    background: white;
  }

  .specs-header-cell {
    position: sticky;
    left: 0;
    z-index: 20;
    background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
    color: white;
    font-weight: bold;
    text-align: left;
    padding: 16px 12px;
    min-width: 200px;
    max-width: 200px;
    border-right: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  }

  .specs-model-header {
    background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
    color: white;
    font-weight: bold;
    text-align: center;
    padding: 16px 8px;
    min-width: 120px;
    position: relative;
  }

  .specs-category-cell {
    position: sticky;
    left: 0;
    z-index: 15;
    padding: 12px;
    min-width: 200px;
    max-width: 200px;
    border-right: 1px solid #e2e8f0;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.05);
    font-weight: 600;
  }

  .specs-data-cell {
    padding: 12px 8px;
    text-align: center;
    min-width: 120px;
    border-bottom: 1px solid #f1f5f9;
    font-size: 14px;
    line-height: 1.4;
  }

  .specs-header-row {
    background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
  }

  .specs-category-header {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    font-weight: bold;
    color: #1e40af;
  }

  .specs-row:nth-child(even) {
    background-color: #f8fafc;
  }

  .specs-row:hover {
    background-color: #e0f2fe;
  }

  @media (max-width: 768px) {
    .specs-table {
      min-width: 700px;
      font-size: 13px;
    }

    .specs-header-cell {
      min-width: 160px;
      max-width: 160px;
      padding: 12px 8px;
      font-size: 12px;
    }

    .specs-category-cell {
      min-width: 160px;
      max-width: 160px;
      padding: 10px 8px;
      font-size: 12px;
    }

    .specs-model-header {
      min-width: 100px;
      padding: 12px 6px;
      font-size: 11px;
    }

    .specs-data-cell {
      min-width: 100px;
      padding: 10px 6px;
      font-size: 11px;
    }
  }

  @media (max-width: 640px) {
    .specs-table {
      min-width: 600px;
      font-size: 12px;
    }

    .specs-header-cell {
      min-width: 140px;
      max-width: 140px;
      padding: 10px 6px;
      font-size: 11px;
    }

    .specs-category-cell {
      min-width: 140px;
      max-width: 140px;
      padding: 8px 6px;
      font-size: 11px;
    }

    .specs-model-header {
      min-width: 90px;
      padding: 10px 4px;
      font-size: 10px;
    }

    .specs-data-cell {
      min-width: 90px;
      padding: 8px 4px;
      font-size: 10px;
    }
  }
`;


const ProductSpecification = () => {
  const [activeTab, setActiveTab] = useState('features');
  const [hoveredModel, setHoveredModel] = useState(null);
  const [scrollPosition, setScrollPosition] = useState(0);

  // Custom tab change handler with scroll position preservation
  const handleTabChange = (tabId: string) => {
    // Store current scroll position
    setScrollPosition(window.scrollY);
    // Update active tab
    setActiveTab(tabId);
  };

  // Custom model selection handler with scroll position preservation
  const handleModelChange = (modelKey: string | null) => {
    // Store current scroll position
    setScrollPosition(window.scrollY);
    // Update hovered model
    setHoveredModel(modelKey);
  };

  // Add custom styles to document head for mobile scrolling
  useEffect(() => {
    const styleElement = document.createElement('style');
    styleElement.textContent = customStyles;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  // Prevent scroll reset on mouse wheel
  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      // Prevent default only if needed
      if ((e.target as Element).closest('.overflow-x-auto')) {
        // Allow natural scrolling in tables with horizontal scroll
        return;
      }
    };

    window.addEventListener('wheel', handleWheel, { passive: true });
    return () => window.removeEventListener('wheel', handleWheel);
  }, []);

  // Prevent default behavior on all buttons to avoid page reloads
  useEffect(() => {
    const handleClick = (e: MouseEvent) => {
      const target = e.target as Element;
      if (target.closest('button') && !target.closest('a')) {
        // This prevents any button from triggering a form submission or page reload
        // But allows anchor tags to work normally
        e.preventDefault();
      }
    };

    window.addEventListener('click', handleClick, { capture: true });
    return () => window.removeEventListener('click', handleClick, { capture: true });
  }, []);

  // Restore scroll position after tab or model changes
  useEffect(() => {
    // Small delay to ensure DOM has updated
    const timer = setTimeout(() => {
      if (scrollPosition > 0) {
        window.scrollTo({
          top: scrollPosition,
          behavior: 'auto' // Use 'auto' instead of 'smooth' to prevent animation
        });
      }
    }, 10);

    return () => clearTimeout(timer);
  }, [activeTab, hoveredModel, scrollPosition]);

  const tabs = [
    { id: 'features', label: 'Features' },
    { id: 'advantages', label: 'Advantages' },
    { id: 'benefits', label: 'Benefits' }
  ];

  const featuresList = [
    { title: 'Wide input voltage range (120 - 280 VAC)', desc: 'Protects against unstable input' },
    { title: 'Extended runtime with multiple unit expansion (N+X)', desc: 'Customizable to your needs' },
    { title: 'Scalable internal design & footprint with fast recovery', desc: 'Easy installation and service' },
    { title: 'Frequency range (50 - 70 Hz)', desc: 'Immune to unstable sources' },
    { title: 'Dual feed capability', desc: 'Provides redundant configuration' },
    { title: 'Parallel capability & high-tier load capacity', desc: 'Scales with your requirements' },
    { title: 'Online Double conversion with Advanced dual-core DSP control', desc: 'Full Digital control for highest performance' },
    { title: 'Remote controlling through IGBT', desc: 'Built-in electronic protection' },
    { title: 'Advanced battery management', desc: 'Automatic battery test including deep discharge protection' },
    { title: 'Compact footprint for unmatched applications', desc: 'Space-efficient design' },
    { title: 'Transformerless Design', desc: 'Enhanced efficiency' },
    { title: 'Advanced Power Factor Correction (0.99) for PF', desc: 'Optimized electrical efficiency' },
    { title: 'Low Technical Load', desc: 'Maximum usable power' }
  ];

  const advantagesList = [
    { title: 'Maintenance Bypass Switch (optional)', desc: 'Inbuilt Battery Cabinet' },
    { title: 'Current Generator Overload protection', desc: 'Prevents issues due to starting inrush currents' },
    { title: 'Ideal for sensitive medical equipment', desc: 'Reliable clean power' },
    { title: 'On-line double conversion & full Digital Frequency Converter', desc: 'Complete emergency coverage mode' },
    { title: 'Built-in system protection diagnostic', desc: 'SNMP / USB Option compatibility' },
    { title: 'Advance backfeed protection circuit design', desc: 'Various operating modes access diverse' },
    { title: 'Power protection concept with regenerating capability', desc: 'Adjustable response under varying load conditions' },
    { title: 'Output frequency freely selectable', desc: 'For sensitive loads and industrial equipment' },
    { title: 'Built-in DC fuses', desc: 'Advance Battery based voltage and analyzer function' },
    { title: 'Built-in internal battery life extender', desc: 'Redundancy & load stability' },
    { title: 'Enhanced bypass fix', desc: 'Multiple operational modes for your needs' },
    { title: 'Low Operating Cost', desc: 'High Efficiency - Up to 95+% in offline mode, 93% in online mode' },
    { title: 'Reduction in carbon footprint', desc: 'Smaller sizes than legacy systems' },
    { title: 'Maximum utilization of UPS capacity', desc: 'Optimized power usage' },
    { title: 'Better efficiency with lower heat', desc: 'Saving up to 40% on energy costs' },
    { title: 'Convenient floor space', desc: 'Compact design' }
  ];

  const benefitsList = [
    {
      title: 'High Uptime / Availability',
      desc: 'Ensures your critical systems remain operational with minimal to zero interruption, delivering maximum operational continuity.'
    },
    {
      title: 'High Flexibility',
      desc: 'Adapts to various configurations and settings, supporting a wide range of devices and operational environments.'
    },
    {
      title: 'High Reliability',
      desc: 'Engineered with premium components and advanced protection systems to ensure consistent and dependable performance.'
    },
    {
      title: 'Low Total Cost of Ownership (TCO)',
      desc: 'Combines energy efficiency, extended lifespan, and reduced maintenance requirements to minimize long-term costs.'
    }
  ];

  const specifications = [
    { category: 'MODEL', model1: 'EL - 1K', model2: 'EL - 2K', model3: 'ELB - 1K', model4: 'ELB - 2K', model5: 'ELB - 3K' },
    { category: 'Rated Capacity', model1: '1 kVA / 1 kW', model2: '2 kVA / 2 kW', model3: '1 kVA / 0.9 kW', model4: '2 kVA / 1.8 kW', model5: '3 kVA / 2.7 kW' },
    { category: 'INPUT', model1: '', model2: '', model3: '', model4: '', model5: '' },
    { category: 'Phase', model1: 'Single (1Ph+N+PE)', model2: 'Single (1Ph+N+PE)', model3: 'Single (1Ph+N+PE)', model4: 'Single (1Ph+N+PE)', model5: 'Single (1Ph+N+PE)' },
    { category: 'Rated Voltage', model1: '208 / 220 / 230 / 240 VAC', model2: '208 / 220 / 230 / 240 VAC', model3: '208 / 220 / 230 / 240 VAC', model4: '208 / 220 / 230 / 240 VAC', model5: '208 / 220 / 230 / 240 VAC' },
    { category: 'Voltage Range', model1: '175 ~ 175 VAC (linear de-rating between 80% and 100% load); 120 ~ 175 VAC (load up to 80%)', model2: '175 ~ 175 VAC (linear de-rating between 80% and 100% load); 120 ~ 175 VAC (load up to 80%)', model3: '175 ~ 175 VAC (linear de-rating between 80% and 100% load); 120 ~ 175 VAC (load up to 80%)', model4: '175 ~ 175 VAC (linear de-rating between 80% and 100% load); 120 ~ 175 VAC (load up to 80%)', model5: '175 ~ 175 VAC (linear de-rating between 80% and 100% load); 120 ~ 175 VAC (load up to 80%)' },
    { category: 'Frequency', model1: '40 / 70 Hz (auto-sensing)', model2: '40 / 70 Hz (auto-sensing)', model3: '40 / 70 Hz (auto-sensing)', model4: '40 / 70 Hz (auto-sensing)', model5: '40 / 70 Hz (auto-sensing)' },
    { category: 'Power Factor', model1: '≥ 0.99', model2: '≥ 0.99', model3: '≥ 0.99', model4: '≥ 0.99', model5: '≥ 0.99' },
    { category: 'Bypass Voltage Range', model1: '±10% (Adjustable)', model2: '±10% (Adjustable)', model3: '±10% (Adjustable)', model4: '±10% (Adjustable)', model5: '±10% (Adjustable)' },
    { category: 'Total Harmonic Distortion (THDi)', model1: '< 3%', model2: '< 3%', model3: '< 3%', model4: '< 3%', model5: '< 3%' },
    { category: 'OUTPUT', model1: '', model2: '', model3: '', model4: '', model5: '' },
    { category: 'Output Wiring', model1: 'Single-phase (1Ph + N + PE)', model2: 'Single-phase (1Ph + N + PE)', model3: 'Single-phase (1Ph + N + PE)', model4: 'Single-phase (1Ph + N + PE)', model5: 'Single-phase (1Ph + N + PE)' },
    { category: 'Rated Voltage', model1: '208 / 220 / 230 / 240 VAC (configurable via LCD)', model2: '208 / 220 / 230 / 240 VAC (configurable via LCD)', model3: '208 / 220 / 230 / 240 VAC (configurable via LCD)', model4: '208 / 220 / 230 / 240 VAC (configurable via LCD)', model5: '208 / 220 / 230 / 240 VAC (configurable via LCD)' },
    { category: 'Voltage Regulation', model1: '±1%', model2: '±1%', model3: '±1%', model4: '±1%', model5: '±1%' },
    { category: 'Frequency', model1: '47 ~ 53Hz or 57 ~ 63Hz (Synchronized with utility); 50/60 Hz ±0.1Hz (Battery mode)', model2: '47 ~ 53Hz or 57 ~ 63Hz (Synchronized with utility); 50/60 Hz ±0.1Hz (Battery mode)', model3: '47 ~ 53Hz or 57 ~ 63Hz (Synchronized with utility); 50/60 Hz ±0.1Hz (Battery mode)', model4: '47 ~ 53Hz or 57 ~ 63Hz (Synchronized with utility); 50/60 Hz ±0.1Hz (Battery mode)', model5: '47 ~ 53Hz or 57 ~ 63Hz (Synchronized with utility); 50/60 Hz ±0.1Hz (Battery mode)' },
    { category: 'Waveform', model1: 'Sinusoidal', model2: 'Sinusoidal', model3: 'Sinusoidal', model4: 'Sinusoidal', model5: 'Sinusoidal' },
    { category: 'Power Factor', model1: '1.0', model2: '1.0', model3: '0.9', model4: '0.9', model5: '0.9' },
    { category: 'Total Harmonic Distortion (THDv)', model1: '< 2%', model2: '< 2%', model3: '< 2%', model4: '< 2%', model5: '< 2%' },
    { category: 'Crest Factor', model1: '3:1', model2: '3:1', model3: '3:1', model4: '3:1', model5: '3:1' },
    { category: 'Overload', model1: '100% ~ 110% for 30 min, 110% ~ 130% for 5 min, 130% ~ 140% for 10 sec, >140% for 1 sec', model2: '100% ~ 110% for 30 min, 110% ~ 130% for 5 min, 130% ~ 140% for 10 sec, >140% for 1 sec', model3: '100% ~ 110% for 30 min, 110% ~ 130% for 5 min, 130% ~ 140% for 10 sec, >140% for 1 sec', model4: '100% ~ 110% for 30 min, 110% ~ 130% for 5 min, 130% ~ 140% for 10 sec, >140% for 1 sec', model5: '100% ~ 110% for 30 min, 110% ~ 130% for 5 min, 130% ~ 140% for 10 sec, >140% for 1 sec' },
    { category: 'BATTERIES', model1: '', model2: '', model3: '', model4: '', model5: '' },
    { category: 'DC Voltage', model1: '36 V', model2: '72 V', model3: '36 V', model4: '72 V', model5: '96 V' },
    { category: 'No. of Batteries', model1: '3 pcs', model2: '6 pcs', model3: '3 pcs', model4: '6 pcs', model5: '8 pcs' },
    { category: 'Charging Current (max)', model1: '1A (1.5A for standard)', model2: '1A (1.5A for standard)', model3: 'Based on charger rating installed in a tower or cabinet format (depends on the battery rating)', model4: 'Based on charger rating installed in a tower or cabinet format (depends on the battery rating)', model5: 'Based on charger rating installed in a tower or cabinet format (depends on the battery rating)' },
    { category: 'DISPLAY', model1: '', model2: '', model3: '', model4: '', model5: '' },
    { category: 'Efficiency', model1: '> 91% (Online mode), > 98% (ECO mode)', model2: '> 91% (Online mode), > 98% (ECO mode)', model3: '> 92% (Online mode), > 98% (ECO mode), > 93% (Battery mode)', model4: '> 92% (Online mode), > 98% (ECO mode), > 93% (Battery mode)', model5: '> 92% (Online mode), > 98% (ECO mode), > 93% (Battery mode)' },
    { category: 'Transfer Time', model1: 'Mains mode to Battery mode: 0 ms', model2: 'Mains mode to Battery mode: 0 ms', model3: 'Mains mode to Battery mode: 0 ms', model4: 'Mains mode to Battery mode: 0 ms', model5: 'Mains mode to Battery mode: 0 ms' },
    { category: 'Protection', model1: 'Short-circuit, Over load, Over temperature, Battery low voltage, Over voltage, Under voltage, Fan failure protection', model2: 'Short-circuit, Over load, Over temperature, Battery low voltage, Over voltage, Under voltage, Fan failure protection', model3: 'Short-circuit, Over load, Over temperature, Battery low voltage, Over voltage, Under voltage, Fan failure protection', model4: 'Short-circuit, Over load, Over temperature, Battery low voltage, Over voltage, Under voltage, Fan failure protection', model5: 'Short-circuit, Over load, Over temperature, Battery low voltage, Over voltage, Under voltage, Fan failure protection' },
    { category: 'Display', model1: 'LCD + LED', model2: 'LCD + LED', model3: 'LCD + LED', model4: 'LCD + LED', model5: 'LCD + LED' },
    { category: 'OTHERS', model1: '', model2: '', model3: '', model4: '', model5: '' },
    { category: 'Operating Temperature', model1: '0°C ~ 40°C', model2: '0°C ~ 40°C', model3: '0°C ~ 40°C', model4: '0°C ~ 40°C', model5: '0°C ~ 40°C' },
    { category: 'Storage Temperature', model1: '-25°C ~ 55°C (without battery)', model2: '-25°C ~ 55°C (without battery)', model3: '-25°C ~ 55°C (without battery)', model4: '-25°C ~ 55°C (without battery)', model5: '-25°C ~ 55°C (without battery)' },
    { category: 'Relative Humidity', model1: '0% ~ 95% (non-condensing)', model2: '0% ~ 95% (non-condensing)', model3: '0% ~ 95% (non-condensing)', model4: '0% ~ 95% (non-condensing)', model5: '0% ~ 95% (non-condensing)' },
    { category: 'Altitude', model1: '< 1000 m, derating 1% for each additional 100 m', model2: '< 1000 m, derating 1% for each additional 100 m', model3: '< 1000 m, derating 1% for each additional 100 m', model4: '< 1000 m, derating 1% for each additional 100 m', model5: '< 1000 m, derating 1% for each additional 100 m' },
    { category: 'Noise Level at 1m', model1: '≤ 50 dB', model2: '≤ 50 dB', model3: '≤ 50 dB', model4: '≤ 50 dB', model5: '≤ 50 dB' },
    { category: 'Dimensions (W × D × H) (mm)', model1: '144 × 353 × 220', model2: '189 × 399 × 318', model3: '144 × 377 × 216', model4: '144 × 377 × 216', model5: '190 × 410 × 325' },
    { category: 'Net Weight (kg)', model1: '13', model2: '25', model3: '8.4', model4: '15', model5: '26.5' }
  ];

  const renderContent = () => {
    switch(activeTab) {
      case 'features':
        return (
          <div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4 md:gap-5">
              {featuresList.map((feature, index) => (
                <div
                  key={index}
                  className="bg-gradient-to-br from-blue-50 to-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 relative overflow-hidden group border border-blue-100/30"
                >
                  {/* Decorative elements */}
                  <div className="absolute top-0 right-0 w-16 h-16 bg-blue-100/50 rounded-bl-full"></div>

                  <div className="p-3 sm:p-4 relative z-10">
                    <div className="flex items-start gap-2 sm:gap-3">
                      <div
                        className="flex-shrink-0 w-7 h-7 sm:w-8 sm:h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center text-white shadow-md"
                      >
                        <Check size={14} className="sm:hidden" />
                        <Check size={16} className="hidden sm:block" />
                      </div>

                      <div>
                        <h4
                          className="font-bold text-sm sm:text-base text-black mb-1 sm:mb-2"
                        >
                          {feature.title}
                        </h4>

                        {feature.desc && (
                          <p className="text-gray-800 leading-relaxed text-xs sm:text-sm">{feature.desc}</p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Feature count indicator */}
            <div className="mt-6 sm:mt-8 text-center">
              <p className="text-gray-800 font-medium text-sm sm:text-base">
                <span className="text-lg sm:text-xl font-bold text-black">{featuresList.length}</span> premium features available
              </p>
            </div>
          </div>
        );

      case 'advantages':
        return (
          <div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4 md:gap-5">
              {advantagesList.map((advantage, index) => (
                <div
                  key={index}
                  className="bg-gradient-to-br from-blue-50 to-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 relative overflow-hidden group border border-blue-100/30"
                >
                  {/* Decorative elements */}
                  <div className="absolute top-0 right-0 w-16 h-16 bg-blue-100/50 rounded-bl-full"></div>

                  <div className="p-3 sm:p-4 relative z-10">
                    <div className="flex items-start gap-2 sm:gap-3">
                      <div
                        className="flex-shrink-0 w-7 h-7 sm:w-8 sm:h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center text-white shadow-md"
                      >
                        <ArrowUpRight size={14} className="sm:hidden" />
                        <ArrowUpRight size={16} className="hidden sm:block" />
                      </div>

                      <div>
                        <h4
                          className="font-bold text-sm sm:text-base text-black mb-1 sm:mb-2"
                        >
                          {advantage.title}
                        </h4>

                        {advantage.desc && (
                          <p className="text-gray-800 leading-relaxed text-xs sm:text-sm">{advantage.desc}</p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Advantage count indicator */}
            <div className="mt-6 sm:mt-8 text-center">
              <p className="text-gray-800 font-medium text-sm sm:text-base">
                <span className="text-lg sm:text-xl font-bold text-black">{advantagesList.length}</span> competitive advantages
              </p>
            </div>
          </div>
        );

      case 'benefits':
        return (
          <div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4 md:gap-5">
              {benefitsList.map((benefit, index) => (
                <motion.div
                  key={index}
                  className="bg-white rounded-lg shadow-md transition-all duration-300 relative overflow-hidden group"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{
                    y: -3,
                    boxShadow: "0 10px 20px -5px rgba(59, 130, 246, 0.15)"
                  }}
                >
                  {/* Background decorative elements */}
                  <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-50 to-white opacity-50"></div>
                  <div className="absolute -right-8 -top-8 w-32 h-32 bg-blue-100/30 rounded-full"></div>

                  <div className="relative z-10 p-3 sm:p-4 md:p-5">
                    <div className="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4">
                      <motion.div
                        className="flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center text-white shadow-lg"
                        whileHover={{ rotate: 5 }}
                        animate={{
                          scale: [1, 1.02, 1],
                          rotate: [0, 2, 0]
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          repeatType: "reverse"
                        }}
                      >
                        <Award size={16} className="sm:hidden" />
                        <Award size={20} className="hidden sm:block" />
                      </motion.div>

                      <motion.h3
                        className="font-bold text-sm sm:text-base md:text-lg text-black"
                        whileHover={{ x: 2 }}
                        transition={{ type: "spring", stiffness: 300, damping: 10 }}
                      >
                        {benefit.title}
                      </motion.h3>
                    </div>

                    <div className="ml-1 sm:ml-2 pl-4 sm:pl-6 border-l-2 border-blue-100">
                      <p className="text-gray-800 leading-relaxed text-xs sm:text-sm md:text-base">{benefit.desc}</p>
                    </div>

                    {/* Decorative element */}
                    <div className="absolute bottom-3 right-3 w-1.5 h-1.5 bg-blue-400 rounded-full opacity-70"></div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Call to action */}
            <motion.div
              className="mt-6 sm:mt-8 text-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
            >
              <p className="text-gray-800 mb-3 sm:mb-4 text-sm sm:text-base">Experience these benefits with our premium UPS solutions</p>
              <a
                href="/contact/sales"
                className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 sm:px-6 py-2.5 sm:py-3 rounded-lg transition-colors duration-300 text-sm sm:text-base"
              >
                <span>Request a Demo</span>
                <ArrowRight size={14} className="sm:hidden" />
                <ArrowRight size={16} className="hidden sm:block" />
              </a>
            </motion.div>
          </div>
        );

      default:
        return null;
    }
  };



  // PDF URL for brochure
  const pdfUrl = "/Krykard Online UPS January 2025. (1).pdf";

  const ProductSpecContent = () => (
    <>      {/* Hero Section with Image on Right and Content on Left */}
      <section className="py-8 sm:py-12 relative overflow-hidden">
        <div className="absolute top-20 left-10 w-64 h-64 bg-blue-200 rounded-full opacity-20 blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-80 h-80 bg-blue-200 rounded-full opacity-20 blur-3xl"></div>

        <div className="relative z-10 px-3 sm:px-4 max-w-7xl mx-auto">          <motion.div
            className="text-black p-3 sm:p-4 md:p-6 overflow-hidden relative mb-6 sm:mb-8 md:mb-10 text-center"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
          >
            <div className="relative z-10 max-w-4xl mx-auto">
              <motion.h1
                className="text-xl sm:text-2xl md:text-4xl lg:text-5xl font-extrabold tracking-tight mb-3 sm:mb-4 text-black"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.2 }}
              >
                KRYKARD EL / ELB <span className="text-gray-800">1/1 UPS</span>
              </motion.h1>

              <motion.p
                className="text-sm sm:text-base md:text-lg font-medium mb-3 sm:mb-4 md:mb-6 text-gray-800 mx-auto max-w-2xl"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.3 }}
              >
                Reliable power solutions for uninterrupted performance
              </motion.p>

              <motion.div
                className="bg-gradient-to-r from-blue-50 to-blue-100 text-black font-bold py-2 sm:py-2.5 md:py-3 px-3 sm:px-4 md:px-6 rounded-full inline-block shadow-sm transform hover:scale-105 transition-transform duration-300 border border-blue-200 text-xs sm:text-sm"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.4 }}
              >
                RELIABLE POWER SOLUTIONS FOR CRITICAL APPLICATIONS
              </motion.div>
            </div>
          </motion.div>{/* Hero Content Area - Right image, Left content */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 md:gap-10 items-center mb-6 sm:mb-8 md:mb-10">            {/* Right side: UPS Image with simple up-down animation */}
            <motion.div
              className="relative flex justify-center order-2 lg:order-1"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
            >
              {/* Product image with simple up-down animation */}
              <motion.div
                animate={{
                  y: [0, -15, 0]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  repeatType: "reverse",
                  ease: "easeInOut"
                }}
              >
                <img
                  src="/UPS/SB_6-removebg-preview.png"
                  alt="EL/ELB Series UPS Units"
                  className="w-full max-w-xl lg:max-w-2xl h-auto"
                />
              </motion.div>
            </motion.div>

            {/* Left side: Content */}
            <motion.div
              className="space-y-4 sm:space-y-6 order-1 lg:order-2"
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-black mb-3 sm:mb-4">Enterprise-Grade Power Protection</h2>
                <div className="h-1 w-16 sm:w-20 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full mb-4 sm:mb-5"></div>
                <p className="text-sm sm:text-base md:text-lg text-gray-800 leading-relaxed">
                  The KRYKARD EL/ELB 1/1 UPS delivers reliable power protection for your mission-critical equipment, ensuring continuous operation during power disturbances with advanced technology and robust engineering.
                </p>
              </motion.div>

              <motion.div
                className="bg-gradient-to-r from-transparent to-blue-50 p-4 sm:p-5 rounded-lg"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <h3 className="text-base sm:text-lg md:text-xl font-bold mb-3 sm:mb-4 text-black">Perfect for:</h3>
                <ul className="space-y-2 sm:space-y-3">
                  {[
                    {icon: "🏢", text: "Small to Medium Office Environments"},
                    {icon: "🏥", text: "Medical Equipment & Healthcare Facilities"},
                    {icon: "💻", text: "IT Infrastructure & Network Equipment"},
                    {icon: "🏭", text: "Industrial Control Systems"},
                    {icon: "🔌", text: "Sensitive Electronic Equipment"}
                  ].map((item, index) => (
                    <motion.li
                      key={index}
                      className="flex items-center group"
                      initial={{ opacity: 0, x: 10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: 0.4 + index * 0.1 }}
                    >
                      <motion.span
                        className="text-lg sm:text-xl mr-3 transform group-hover:scale-110 transition-transform bg-blue-50 w-8 h-8 sm:w-9 sm:h-9 flex items-center justify-center rounded-full"
                        animate={{ rotate: [0, 8, 0] }}
                        transition={{ duration: 6, repeat: Infinity, repeatType: "reverse" }}
                      >
                        {item.icon}
                      </motion.span>
                      <span className="text-gray-800 font-medium group-hover:text-black transition-colors text-sm sm:text-base">
                        {item.text}
                      </span>
                    </motion.li>
                  ))}
                </ul>
              </motion.div>

              <motion.div
                className="flex flex-col sm:flex-row gap-3 sm:gap-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <motion.a
                  href="/contact/sales"
                  className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-4 sm:px-6 py-2.5 sm:py-3 rounded-lg shadow-lg flex items-center justify-center gap-2 transition-all duration-300 text-sm sm:text-base"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <span>Request Quote</span>
                  <ArrowRight size={16} />
                </motion.a>

                <motion.button
                  className="border-2 border-blue-600 text-black hover:bg-blue-50 px-4 sm:px-6 py-2.5 sm:py-3 rounded-lg shadow-lg transition-all duration-300 flex items-center justify-center gap-2 text-sm sm:text-base"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => window.open(pdfUrl, "_blank")}
                >
                  <FileText size={16} />
                  <span>View Brochure</span>
                </motion.button>
              </motion.div>
            </motion.div>
          </div>

          {/* Key Features Section - Modern & Enhanced Design */}
          <div className="mb-12 sm:mb-16 relative">
            {/* Background decorative elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
              <div className="absolute -top-20 -left-20 w-64 h-64 bg-blue-200 rounded-full opacity-20 blur-3xl"></div>
              <div className="absolute -bottom-20 -right-20 w-80 h-80 bg-blue-300 rounded-full opacity-20 blur-3xl"></div>
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-40 bg-gradient-to-r from-blue-200 to-blue-300 opacity-10 blur-3xl"></div>
            </div>

            <motion.div
              className="text-center mb-8 sm:mb-10 relative z-10"
              initial={{ opacity: 0, y: -20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true, margin: "-100px" }}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                whileInView={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-black mb-2 sm:mb-3 inline-block relative">
                  Key Features
                  <motion.div
                    className="absolute -bottom-1 sm:-bottom-2 left-0 right-0 h-0.5 sm:h-1 bg-gradient-to-r from-blue-400 via-blue-600 to-blue-400"
                    initial={{ scaleX: 0, opacity: 0 }}
                    whileInView={{ scaleX: 1, opacity: 1 }}
                    transition={{ duration: 0.8, delay: 0.4 }}
                    viewport={{ once: true }}
                  />
                </h2>
              </motion.div>
              <motion.p
                className="mt-4 sm:mt-5 text-sm sm:text-base md:text-lg text-gray-800 max-w-2xl mx-auto font-medium"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.3 }}
                viewport={{ once: true }}
              >
                Advanced capabilities that define our premium UPS solutions
              </motion.p>
            </motion.div>

            {/* Enhanced Feature Cards with 3D effects */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-5 md:gap-6 relative z-10">
              {[
                {
                  icon: <Zap size={20} />,
                  value: "Online",
                  suffix: "",
                  title: "Double Conversion",
                  description: "Pure sine wave output with zero transfer time ensures complete protection for critical equipment",
                  color: "from-blue-500 to-blue-600",
                  bgGlow: "from-blue-400/20 via-blue-500/10 to-transparent"
                },
                {
                  icon: <Shield size={20} />,
                  value: "98",
                  suffix: "%",
                  title: "ECO Mode Efficiency",
                  description: "Energy-saving mode reduces operational costs while maintaining essential protection",
                  color: "from-green-500 to-blue-500",
                  bgGlow: "from-green-400/20 via-blue-500/10 to-transparent"
                },
                {
                  icon: <Clock size={20} />,
                  value: "24/7",
                  suffix: "",
                  title: "Continuous Protection",
                  description: "Reliable power protection that safeguards your equipment around the clock",
                  color: "from-blue-600 to-indigo-600",
                  bgGlow: "from-blue-500/20 via-indigo-500/10 to-transparent"
                }
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  className="bg-white rounded-xl shadow-lg relative overflow-hidden group"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.7, delay: index * 0.1 }}
                  viewport={{ once: true, margin: "-100px" }}
                  whileHover={{
                    y: -5,
                    transition: { duration: 0.3 }
                  }}
                >
                  {/* Gradient top border */}
                  <div className={`absolute top-0 left-0 right-0 h-1 bg-gradient-to-r ${feature.color}`}></div>

                  {/* Background glow effect */}
                  <div className={`absolute -top-20 -right-20 w-32 h-32 bg-gradient-to-br ${feature.bgGlow} rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700`}></div>

                  <div className="p-4 sm:p-5 md:p-6 relative z-10">
                    {/* Icon with animated background */}
                    <div className="mb-4 sm:mb-5 relative">
                      <motion.div
                        className={`absolute inset-0 bg-gradient-to-r ${feature.color} rounded-full opacity-10 group-hover:opacity-20 transition-opacity duration-300`}
                        animate={{
                          scale: [1, 1.1, 1],
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          repeatType: "reverse"
                        }}
                      />
                      <div className="relative z-10 w-12 h-12 sm:w-14 sm:h-14 flex items-center justify-center rounded-full bg-gradient-to-br from-white to-blue-50 shadow-md border border-blue-100">
                        <motion.div
                          animate={{
                            rotate: [0, 5, 0, -5, 0],
                          }}
                          transition={{
                            duration: 6,
                            repeat: Infinity,
                            repeatType: "reverse"
                          }}
                        >
                          {React.cloneElement(feature.icon, { className: `text-blue-600` })}
                        </motion.div>
                      </div>
                    </div>

                    {/* Feature value with animated counting effect */}
                    {feature.value && (
                      <motion.div
                        className="mb-3 sm:mb-4"
                        initial={{ opacity: 0, scale: 0.8 }}
                        whileInView={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
                        viewport={{ once: true }}
                      >
                        <div className="flex items-center justify-center">
                          <motion.span
                            className="text-2xl sm:text-3xl font-extrabold text-blue-700"
                            initial={{ opacity: 0 }}
                            whileInView={{ opacity: 1 }}
                            transition={{ duration: 1, delay: 0.5 + index * 0.2 }}
                            viewport={{ once: true }}
                          >
                            {feature.value}
                          </motion.span>
                          <motion.span
                            className="text-lg sm:text-xl font-bold text-blue-600 ml-1"
                            initial={{ opacity: 0 }}
                            whileInView={{ opacity: 1 }}
                            transition={{ duration: 1, delay: 0.7 + index * 0.2 }}
                            viewport={{ once: true }}
                          >
                            {feature.suffix}
                          </motion.span>
                        </div>
                      </motion.div>
                    )}

                    {/* Title with hover effect */}
                    <motion.h3
                      className="text-base sm:text-lg md:text-xl font-bold text-black mb-2 sm:mb-3 relative inline-block"
                      initial={{ opacity: 0 }}
                      whileInView={{ opacity: 1 }}
                      transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
                      viewport={{ once: true }}
                    >
                      {feature.title}
                      <div className="absolute bottom-0 left-0 w-0 h-0.5 bg-blue-500 group-hover:w-full transition-all duration-300"></div>
                    </motion.h3>

                    {/* Description with fade-in effect */}
                    <motion.p
                      className="text-gray-800 leading-relaxed text-sm sm:text-base"
                      initial={{ opacity: 0 }}
                      whileInView={{ opacity: 1 }}
                      transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}
                      viewport={{ once: true }}
                    >
                      {feature.description}
                    </motion.p>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Additional features section */}
            <motion.div
              className="mt-10 sm:mt-12 text-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true, margin: "-100px" }}
            >
              <h3 className="text-lg sm:text-xl md:text-2xl font-bold text-black mb-6 sm:mb-8">Additional Premium Features</h3>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4">
                {[
                  { icon: "🔋", text: "Extended Battery Life" },
                  { icon: "🔌", text: "Wide Input Voltage Range" },
                  { icon: "🔄", text: "Fast Recovery Time" },
                  { icon: "🛡️", text: "Advanced Protection" }
                ].map((item, idx) => (
                  <motion.div
                    key={idx}
                    className="bg-blue-50 rounded-lg p-3 sm:p-4 shadow-sm hover:shadow-md transition-all duration-300 hover:bg-blue-100/50"
                    initial={{ opacity: 0, scale: 0.9 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: idx * 0.1 }}
                    viewport={{ once: true }}
                    whileHover={{ y: -3 }}
                  >
                    <motion.div
                      className="text-2xl sm:text-3xl mb-2 sm:mb-3"
                      animate={{
                        y: [0, -3, 0],
                        rotate: [-1, 1, -1]
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        repeatType: "reverse"
                      }}
                    >
                      {item.icon}
                    </motion.div>
                    <p className="text-gray-800 font-medium text-xs sm:text-sm">{item.text}</p>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Modern Tabs Section with Enhanced Design */}
      <section className="max-w-7xl mx-auto px-3 sm:px-4 mb-12 sm:mb-16 relative">
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-40 left-1/4 w-72 h-72 bg-blue-100 rounded-full opacity-30 blur-3xl"></div>
          <div className="absolute -bottom-20 right-1/4 w-64 h-64 bg-blue-200 rounded-full opacity-20 blur-3xl"></div>
        </div>

        <motion.div
          className="text-center mb-8 sm:mb-10 relative z-10"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <motion.h2
            className="text-2xl sm:text-3xl md:text-4xl font-bold text-black mb-2 sm:mb-3"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            Product Details
          </motion.h2>
          <motion.div
            className="h-0.5 sm:h-1 w-16 sm:w-20 md:w-24 bg-gradient-to-r from-blue-400 to-blue-600 mx-auto rounded-full"
            initial={{ width: 0, opacity: 0 }}
            whileInView={{ width: 96, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          ></motion.div>
          <motion.p
            className="mt-3 sm:mt-4 text-sm sm:text-base md:text-lg text-gray-800 max-w-2xl mx-auto"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            viewport={{ once: true }}
          >
            Explore the comprehensive details of our UPS solutions
          </motion.p>
        </motion.div>

        {/* Modern Tab Navigation */}
        <div className="relative z-10 mb-8 sm:mb-10">
          <div className="flex justify-center">
            <div className="inline-flex flex-wrap justify-center gap-1 sm:gap-2 bg-blue-50 p-1 sm:p-1.5 rounded-lg sm:rounded-xl shadow-md">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  className={`relative py-2 sm:py-2.5 px-3 sm:px-6 md:px-8 font-medium text-xs sm:text-sm md:text-base rounded-md sm:rounded-lg ${
                    activeTab === tab.id
                      ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white'
                      : 'text-black hover:text-gray-900 hover:bg-blue-50'
                  }`}
                  onClick={(e) => {
                    // Prevent any default behavior
                    e.preventDefault();
                    e.stopPropagation();
                    // Use custom handler to preserve scroll position
                    handleTabChange(tab.id);
                  }}
                  type="button" // Explicitly set button type to prevent form submission behavior
                >
                  {/* Tab label */}
                  {tab.label}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Tab Content Container - No Animation */}
        <div className="relative z-10 bg-white rounded-lg sm:rounded-xl shadow-lg p-3 sm:p-4 md:p-6 overflow-hidden">
          {/* Decorative corner accents */}
          <div className="absolute top-0 left-0 w-12 h-12 sm:w-16 sm:h-16 bg-blue-50 rounded-br-2xl sm:rounded-br-3xl"></div>
          <div className="absolute bottom-0 right-0 w-12 h-12 sm:w-16 sm:h-16 bg-blue-50 rounded-tl-2xl sm:rounded-tl-3xl"></div>

          {/* Content without animations */}
          <div className="relative min-h-[300px] sm:min-h-[400px]">
            <div className="relative z-10 w-full">
              {renderContent()}
            </div>
          </div>
        </div>
      </section>

      {/* Modern Technical Specifications Section */}
      <section className="w-full mb-12 sm:mb-16 px-3 sm:px-4 md:px-6 lg:px-8 overflow-hidden relative">
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-40 right-1/4 w-72 h-72 bg-blue-100 rounded-full opacity-30 blur-3xl"></div>
          <div className="absolute -bottom-20 left-1/4 w-64 h-64 bg-blue-200 rounded-full opacity-20 blur-3xl"></div>
        </div>

        <motion.div
          className="text-center mb-8 sm:mb-10 relative z-10"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-black mb-2 sm:mb-3 inline-block relative">
              Technical Specifications
              <motion.div
                className="absolute -bottom-1 sm:-bottom-2 left-0 right-0 h-0.5 sm:h-1 bg-gradient-to-r from-blue-400 via-blue-600 to-blue-400"
                initial={{ scaleX: 0, opacity: 0 }}
                whileInView={{ scaleX: 1, opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
              />
            </h2>
          </motion.div>
          <motion.p
            className="mt-4 sm:mt-5 text-sm sm:text-base md:text-lg text-gray-800 max-w-2xl mx-auto font-medium"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.3 }}
            viewport={{ once: true }}
          >
            Comprehensive technical details for the EL/ELB Series UPS line
          </motion.p>
        </motion.div>

        {/* Model Selection Tabs */}
        <div className="relative z-10 mb-6 sm:mb-8">
          <div className="flex justify-center flex-wrap">
            <div className="flex flex-wrap justify-center gap-1 sm:gap-2 bg-blue-50 p-1 sm:p-1.5 rounded-lg shadow-md mb-4 sm:mb-6">
              {['All Models', 'EL - 1K', 'EL - 2K', 'ELB - 1K', 'ELB - 2K', 'ELB - 3K'].map((model, index) => {
                const modelKey = index === 0 ? null : `model${index}`;
                return (
                  <button
                    key={index}
                    className={`relative py-1.5 sm:py-2 px-2 sm:px-3 font-medium text-xs sm:text-sm rounded-md ${
                      hoveredModel === modelKey || (index === 0 && hoveredModel === null)
                        ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white'
                        : 'text-black hover:text-gray-900 hover:bg-blue-50'
                    }`}
                    onClick={(e) => {
                      // Prevent any default behavior
                      e.preventDefault();
                      e.stopPropagation();
                      // Use custom handler to preserve scroll position
                      handleModelChange(modelKey);
                    }}
                    type="button" // Explicitly set button type to prevent form submission behavior
                  >
                    {/* Model label */}
                    {model}
                  </button>
                );
              })}
            </div>
          </div>
        </div>

        {/* Interactive Specifications Card */}
        <motion.div
          className="relative z-10 bg-white rounded-lg sm:rounded-xl shadow-lg overflow-hidden w-full"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true, margin: "-100px" }}
        >
          {/* Decorative corner accents */}
          <div className="absolute top-0 left-0 w-12 h-12 sm:w-16 sm:h-16 bg-blue-50 rounded-br-2xl sm:rounded-br-3xl"></div>
          <div className="absolute bottom-0 right-0 w-12 h-12 sm:w-16 sm:h-16 bg-blue-50 rounded-tl-2xl sm:rounded-tl-3xl"></div>

          {/* Specifications Table - Same Design as Web View with Mobile Optimization */}
          <div className="w-full">
            <div className="text-sm text-gray-600 mb-4 p-4 md:hidden bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg text-center font-medium border border-blue-200">
              <span>� Swipe horizontally to view all model specifications →</span>
            </div>
            <div className="specs-table-container">
              <div className="specs-table-scroll">
                <table className="specs-table">
                  <thead>
                    <tr className="bg-gradient-to-r from-blue-800 to-blue-900">
                      <th className="sticky-header-mobile py-3 px-3 text-left" style={{ minWidth: '140px' }}>
                        <div className="font-bold text-white text-xs">SPECIFICATIONS</div>
                      </th>
                      {specifications[0].model1 && (
                        <th className={`py-3 px-2 text-center ${hoveredModel === 'model1' || hoveredModel === null ? 'opacity-100' : 'opacity-70'} transition-opacity duration-300`} style={{ minWidth: '90px' }}>
                          <div className="font-bold text-white text-xs relative">
                            {specifications[0].model1}
                            {(hoveredModel === 'model1' || hoveredModel === null) && (
                              <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-white" />
                            )}
                          </div>
                        </th>
                      )}
                      {specifications[0].model2 && (
                        <th className={`py-3 px-2 text-center ${hoveredModel === 'model2' || hoveredModel === null ? 'opacity-100' : 'opacity-70'} transition-opacity duration-300`} style={{ minWidth: '90px' }}>
                          <div className="font-bold text-white text-xs relative">
                            {specifications[0].model2}
                            {(hoveredModel === 'model2' || hoveredModel === null) && (
                              <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-white" />
                            )}
                          </div>
                        </th>
                      )}
                      {specifications[0].model3 && (
                        <th className={`py-3 px-2 text-center ${hoveredModel === 'model3' || hoveredModel === null ? 'opacity-100' : 'opacity-70'} transition-opacity duration-300`} style={{ minWidth: '90px' }}>
                          <div className="font-bold text-white text-xs relative">
                            {specifications[0].model3}
                            {(hoveredModel === 'model3' || hoveredModel === null) && (
                              <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-white" />
                            )}
                          </div>
                        </th>
                      )}
                      {specifications[0].model4 && (
                        <th className={`py-3 px-2 text-center ${hoveredModel === 'model4' || hoveredModel === null ? 'opacity-100' : 'opacity-70'} transition-opacity duration-300`} style={{ minWidth: '90px' }}>
                          <div className="font-bold text-white text-xs relative">
                            {specifications[0].model4}
                            {(hoveredModel === 'model4' || hoveredModel === null) && (
                              <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-white" />
                            )}
                          </div>
                        </th>
                      )}
                      {specifications[0].model5 && (
                        <th className={`py-3 px-2 text-center ${hoveredModel === 'model5' || hoveredModel === null ? 'opacity-100' : 'opacity-70'} transition-opacity duration-300`} style={{ minWidth: '90px' }}>
                          <div className="font-bold text-white text-xs relative">
                            {specifications[0].model5}
                            {(hoveredModel === 'model5' || hoveredModel === null) && (
                              <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-white" />
                            )}
                          </div>
                        </th>
                      )}
                    </tr>
                  </thead>
                  <tbody>
                    {specifications.slice(1).map((spec, index) => {
                      // Determine if this is a category header row
                      const isHeader = spec.category.includes('INPUT') ||
                                      spec.category.includes('OUTPUT') ||
                                      spec.category.includes('BATTERY') ||
                                      spec.category.includes('DISPLAY') ||
                                      spec.category.includes('OTHERS');

                      return (
                        <tr
                          key={index}
                          className={`border-b ${isHeader ? 'border-blue-200' : 'border-gray-100'} hover:bg-blue-50/30 transition-colors duration-200`}
                        >
                          <td className={`sticky-cell-mobile py-2 px-3 ${
                            isHeader
                              ? 'bg-gradient-to-r from-blue-100 to-blue-50'
                              : index % 2 === 0 ? 'bg-white' : 'bg-gray-50'
                          }`}>
                            <div className={`${
                              isHeader
                                ? 'font-bold text-black text-xs'
                                : 'font-medium text-black text-xs'
                            } ${isHeader ? 'pl-0' : 'pl-1'}`}>
                              {isHeader ? (
                                <div className="flex items-center">
                                  <div className="w-1 h-4 bg-blue-700 rounded-r-md mr-2"></div>
                                  <span className="break-words">{spec.category}</span>
                                </div>
                              ) : (
                                <span className="break-words">{spec.category}</span>
                              )}
                            </div>
                          </td>

                          {spec.model1 !== undefined && (
                            <td className={`py-2 px-2 text-center ${hoveredModel === 'model1' || hoveredModel === null ? 'opacity-100' : 'opacity-90'} transition-opacity duration-300 ${hoveredModel === 'model1' ? 'bg-blue-100/50' : ''}`}>
                              <div className="text-black font-medium text-xs break-words leading-tight">{spec.model1}</div>
                            </td>
                          )}

                          {spec.model2 !== undefined && (
                            <td className={`py-2 px-2 text-center ${hoveredModel === 'model2' || hoveredModel === null ? 'opacity-100' : 'opacity-90'} transition-opacity duration-300 ${hoveredModel === 'model2' ? 'bg-blue-100/50' : ''}`}>
                              <div className="text-black font-medium text-xs break-words leading-tight">{spec.model2}</div>
                            </td>
                          )}

                          {spec.model3 !== undefined && (
                            <td className={`py-2 px-2 text-center ${hoveredModel === 'model3' || hoveredModel === null ? 'opacity-100' : 'opacity-90'} transition-opacity duration-300 ${hoveredModel === 'model3' ? 'bg-blue-100/50' : ''}`}>
                              <div className="text-black font-medium text-xs break-words leading-tight">{spec.model3}</div>
                            </td>
                          )}

                          {spec.model4 !== undefined && (
                            <td className={`py-2 px-2 text-center ${hoveredModel === 'model4' || hoveredModel === null ? 'opacity-100' : 'opacity-90'} transition-opacity duration-300 ${hoveredModel === 'model4' ? 'bg-blue-100/50' : ''}`}>
                              <div className="text-black font-medium text-xs break-words leading-tight">{spec.model4}</div>
                            </td>
                          )}

                          {spec.model5 !== undefined && (
                            <td className={`py-2 px-2 text-center ${hoveredModel === 'model5' || hoveredModel === null ? 'opacity-100' : 'opacity-90'} transition-opacity duration-300 ${hoveredModel === 'model5' ? 'bg-blue-100/50' : ''}`}>
                              <div className="text-black font-medium text-xs break-words leading-tight">{spec.model5}</div>
                            </td>
                          )}
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          </div>


        </motion.div>

        {/* Key specifications highlights */}
        <motion.div
          className="mt-6 sm:mt-8 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-5 relative z-10"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.2 }}
          viewport={{ once: true, margin: "-100px" }}
        >
          {[
            {
              title: "Input Voltage Range",
              value: "120-280 VAC",
              icon: "⚡",
              description: "Wide input voltage range for stable operation in various power conditions"
            },
            {
              title: "Efficiency",
              value: "Up to 98%",
              icon: "🔋",
              description: "High efficiency in ECO mode for reduced operational costs"
            },
            {
              title: "Protection Features",
              value: "Comprehensive",
              icon: "🛡️",
              description: "Complete protection against short-circuit, overload, over temperature, and more"
            }
          ].map((item, idx) => (
            <motion.div
              key={idx}
              className="bg-white rounded-lg shadow-md p-4 sm:p-5 border border-blue-100 hover:shadow-lg transition-all duration-300"
              whileHover={{ y: -3 }}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 + idx * 0.1 }}
              viewport={{ once: true }}
            >
              <div className="flex items-center mb-3 sm:mb-4">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-full flex items-center justify-center text-lg sm:text-xl mr-3 sm:mr-4">
                  {item.icon}
                </div>
                <div>
                  <h3 className="font-bold text-black text-sm sm:text-base">{item.title}</h3>
                  <div className="text-sm sm:text-base font-semibold text-blue-600">{item.value}</div>
                </div>
              </div>
              <p className="text-gray-800 text-xs sm:text-sm">{item.description}</p>
            </motion.div>
          ))}
        </motion.div>
      </section>

      {/* Modern EL vs ELB Series Comparison */}
      <section className="max-w-7xl mx-auto px-3 sm:px-4 mb-12 sm:mb-16 relative">
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-40 left-1/3 w-72 h-72 bg-blue-100 rounded-full opacity-30 blur-3xl"></div>
          <div className="absolute -bottom-20 right-1/3 w-64 h-64 bg-blue-200 rounded-full opacity-20 blur-3xl"></div>
        </div>

        <motion.div
          className="text-center mb-8 sm:mb-10 relative z-10"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-black mb-2 sm:mb-3 inline-block relative">
              EL vs ELB Series Comparison
              <motion.div
                className="absolute -bottom-1 sm:-bottom-2 left-0 right-0 h-0.5 sm:h-1 bg-gradient-to-r from-blue-400 via-blue-600 to-blue-400"
                initial={{ scaleX: 0, opacity: 0 }}
                whileInView={{ scaleX: 1, opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
              />
            </h2>
          </motion.div>
          <motion.p
            className="mt-4 sm:mt-5 text-sm sm:text-base md:text-lg text-gray-800 max-w-2xl mx-auto font-medium"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.3 }}
            viewport={{ once: true }}
          >
            Understand the key differences between our product lines
          </motion.p>
        </motion.div>



        {/* Comparison Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4 md:gap-6 relative z-10">
          {/* EL Series Card */}
          <motion.div
            className="bg-white rounded-lg sm:rounded-xl shadow-lg overflow-hidden relative"
            initial={{ opacity: 0, x: -30, y: 20 }}
            whileInView={{ opacity: 1, x: 0, y: 0 }}
            transition={{
              type: "spring",
              stiffness: 100,
              damping: 20,
              delay: 0.2
            }}
            viewport={{ once: true, margin: "-100px" }}
            whileHover={{ y: -3 }}
          >
            {/* Top gradient bar */}
            <div className="h-1 sm:h-2 bg-gradient-to-r from-blue-500 to-blue-600"></div>

            {/* Header */}
            <div className="bg-gradient-to-br from-blue-500 to-blue-600 p-4 sm:p-5 relative overflow-hidden">
              {/* Decorative elements */}
              <div className="absolute -top-8 -right-8 w-24 h-24 sm:w-32 sm:h-32 bg-white opacity-10 rounded-full"></div>
              <div className="absolute bottom-0 left-0 w-full h-8 bg-gradient-to-r from-blue-600/20 to-transparent"></div>

              <div className="flex items-center relative z-10">
                <motion.div
                  className="w-12 h-12 sm:w-14 sm:h-14 bg-white rounded-xl flex items-center justify-center shadow-lg mr-3 sm:mr-4"
                  whileHover={{ rotate: 3 }}
                  transition={{ type: "spring", stiffness: 300, damping: 10 }}
                >
                  <h3 className="text-lg sm:text-xl font-extrabold text-blue-600">EL</h3>
                </motion.div>
                <div>
                  <h3 className="text-lg sm:text-xl font-bold text-white">EL Series</h3>
                  <p className="text-blue-100 text-sm">Standard UPS Solution</p>
                </div>
              </div>
            </div>

            {/* Features List */}
            <div className="p-4 sm:p-5">
              <ul className="space-y-2 sm:space-y-3">
                {[
                  '1.0 power factor for maximum efficiency',
                  'Compact tower form factor',
                  'Available in 1kVA and 2kVA capacities',
                  'Ideal for small office environments',
                  'Standard battery configuration',
                  'Online Double conversion technology',
                  '> 91% efficiency in Online mode'
                ].map((feature, index) => (
                  <motion.li
                    key={index}
                    className="flex items-start"
                    initial={{ opacity: 0, x: -10 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 * index }}
                    viewport={{ once: true }}
                  >
                    <motion.div
                      className="flex-shrink-0 mt-0.5 mr-2 sm:mr-3 w-5 h-5 sm:w-6 sm:h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      transition={{ type: "spring", stiffness: 300, damping: 10 }}
                    >
                      <Check size={12} className="sm:hidden" />
                      <Check size={14} className="hidden sm:block" />
                    </motion.div>
                    <span className="text-gray-800 text-sm sm:text-base">{feature}</span>
                  </motion.li>
                ))}
              </ul>

              {/* Ideal for section */}
              <motion.div
                className="mt-5 sm:mt-6 bg-blue-50 p-3 sm:p-4 rounded-lg border border-blue-100"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.7 }}
                viewport={{ once: true }}
              >
                <h4 className="font-bold text-black mb-2 text-sm sm:text-base">Ideal For:</h4>
                <p className="text-gray-800 text-xs sm:text-sm">Small offices, workstations, and environments with stable power conditions</p>
              </motion.div>
            </div>
          </motion.div>

          {/* ELB Series Card */}
          <motion.div
            className="bg-white rounded-lg sm:rounded-xl shadow-lg overflow-hidden relative"
            initial={{ opacity: 0, x: 30, y: 20 }}
            whileInView={{ opacity: 1, x: 0, y: 0 }}
            transition={{
              type: "spring",
              stiffness: 100,
              damping: 20,
              delay: 0.3
            }}
            viewport={{ once: true, margin: "-100px" }}
            whileHover={{ y: -3 }}
          >
            {/* Top gradient bar */}
            <div className="h-1 sm:h-2 bg-gradient-to-r from-blue-600 to-blue-800"></div>

            {/* Header */}
            <div className="bg-gradient-to-br from-blue-600 to-blue-800 p-4 sm:p-5 relative overflow-hidden">
              {/* Decorative elements */}
              <div className="absolute -top-8 -right-8 w-24 h-24 sm:w-32 sm:h-32 bg-white opacity-10 rounded-full"></div>
              <div className="absolute bottom-0 left-0 w-full h-8 bg-gradient-to-r from-blue-800/20 to-transparent"></div>

              <div className="flex items-center relative z-10">
                <motion.div
                  className="w-12 h-12 sm:w-14 sm:h-14 bg-white rounded-xl flex items-center justify-center shadow-lg mr-3 sm:mr-4"
                  whileHover={{ rotate: 3 }}
                  transition={{ type: "spring", stiffness: 300, damping: 10 }}
                >
                  <h3 className="text-lg sm:text-xl font-extrabold text-blue-700">ELB</h3>
                </motion.div>
                <div>
                  <h3 className="text-lg sm:text-xl font-bold text-white">ELB Series</h3>
                  <p className="text-blue-100 text-sm">Advanced UPS Solution</p>
                </div>
              </div>
            </div>

            {/* Features List */}
            <div className="p-4 sm:p-5">
              <ul className="space-y-2 sm:space-y-3">
                {[
                  '0.9 power factor for optimal performance',
                  'Extended battery options available',
                  'Available in 1kVA, 2kVA, and 3kVA capacities',
                  'Customizable battery configuration',
                  'Higher efficiency in battery mode (93%)',
                  'Advanced battery management system',
                  'Comprehensive protection features'
                ].map((feature, index) => (
                  <motion.li
                    key={index}
                    className="flex items-start"
                    initial={{ opacity: 0, x: -10 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 * index }}
                    viewport={{ once: true }}
                  >
                    <motion.div
                      className="flex-shrink-0 mt-0.5 mr-2 sm:mr-3 w-5 h-5 sm:w-6 sm:h-6 bg-blue-100 text-blue-700 rounded-full flex items-center justify-center"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      transition={{ type: "spring", stiffness: 300, damping: 10 }}
                    >
                      <Check size={12} className="sm:hidden" />
                      <Check size={14} className="hidden sm:block" />
                    </motion.div>
                    <span className="text-gray-800 text-sm sm:text-base">{feature}</span>
                  </motion.li>
                ))}
              </ul>

              {/* Ideal for section */}
              <motion.div
                className="mt-5 sm:mt-6 bg-blue-50 p-3 sm:p-4 rounded-lg border border-blue-100"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.7 }}
                viewport={{ once: true }}
              >
                <h4 className="font-bold text-black mb-2 text-sm sm:text-base">Ideal For:</h4>
                <p className="text-gray-800 text-xs sm:text-sm">Critical equipment, server rooms, and environments with unstable power conditions</p>
              </motion.div>
            </div>
          </motion.div>
        </div>


      </section>

      {/* Contact Information Section */}
      <section className="max-w-7xl mx-auto px-3 sm:px-4 mb-12 sm:mb-16">
        <motion.div
          className="bg-blue-100 rounded-lg sm:rounded-xl overflow-hidden"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <div className="py-6 sm:py-8 px-4 sm:px-6 text-center">
            <motion.h2
              className="text-xl sm:text-2xl md:text-3xl font-bold text-black mb-3 sm:mb-4"
              initial={{ opacity: 0, y: -10 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              Need More Information?
            </motion.h2>

            <motion.p
              className="text-gray-800 max-w-3xl mx-auto mb-6 sm:mb-8 text-sm sm:text-base"
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
            >
              Our team of experts is ready to help you with product specifications, custom solutions, pricing, and
              any other details you need about the KRYKARD UPS systems.
            </motion.p>

            <motion.div
              className="flex justify-center"
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              viewport={{ once: true }}
            >
              <a
                href="/contact/sales"
                className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 sm:py-3 px-4 sm:px-6 rounded-md transition-colors duration-300 text-sm sm:text-base"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                </svg>
                Contact Our Experts
              </a>
            </motion.div>
          </div>

          {/* Alternative Mobile Card Layout for very small screens */}
          <div className="block sm:hidden mt-6">
            <div className="text-xs text-gray-600 mb-4 p-3 bg-yellow-50 rounded-lg text-center font-medium border border-yellow-200">
              <span>📱 Mobile-optimized view below</span>
            </div>

            {/* Model Selection for Mobile Cards */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-black mb-2">Select Model:</label>
              <select
                className="w-full p-2 border border-gray-300 rounded-lg text-sm bg-white"
                value={hoveredModel || 'all'}
                onChange={(e) => handleModelChange(e.target.value === 'all' ? null : e.target.value)}
              >
                <option value="all">All Models</option>
                <option value="model1">EL - 1K</option>
                <option value="model2">EL - 2K</option>
                <option value="model3">ELB - 1K</option>
                <option value="model4">ELB - 2K</option>
                <option value="model5">ELB - 3K</option>
              </select>
            </div>

            {/* Mobile Cards */}
            <div className="space-y-3">
              {specifications.slice(1).filter(spec => {
                // Filter out empty category headers
                return spec.category && !spec.category.includes('INPUT') &&
                       !spec.category.includes('OUTPUT') &&
                       !spec.category.includes('BATTERY') &&
                       !spec.category.includes('DISPLAY') &&
                       !spec.category.includes('OTHERS');
              }).map((spec, index) => (
                <div key={index} className="bg-white rounded-lg border border-gray-200 p-3 shadow-sm">
                  <h4 className="font-semibold text-black text-sm mb-2 border-b border-gray-100 pb-1">
                    {spec.category}
                  </h4>
                  <div className="grid grid-cols-1 gap-2">
                    {hoveredModel === null && (
                      <>
                        {spec.model1 && <div className="flex justify-between"><span className="text-xs text-gray-600">EL-1K:</span><span className="text-xs text-black font-medium">{spec.model1}</span></div>}
                        {spec.model2 && <div className="flex justify-between"><span className="text-xs text-gray-600">EL-2K:</span><span className="text-xs text-black font-medium">{spec.model2}</span></div>}
                        {spec.model3 && <div className="flex justify-between"><span className="text-xs text-gray-600">ELB-1K:</span><span className="text-xs text-black font-medium">{spec.model3}</span></div>}
                        {spec.model4 && <div className="flex justify-between"><span className="text-xs text-gray-600">ELB-2K:</span><span className="text-xs text-black font-medium">{spec.model4}</span></div>}
                        {spec.model5 && <div className="flex justify-between"><span className="text-xs text-gray-600">ELB-3K:</span><span className="text-xs text-black font-medium">{spec.model5}</span></div>}
                      </>
                    )}
                    {hoveredModel === 'model1' && spec.model1 && <div className="text-sm text-black font-medium">{spec.model1}</div>}
                    {hoveredModel === 'model2' && spec.model2 && <div className="text-sm text-black font-medium">{spec.model2}</div>}
                    {hoveredModel === 'model3' && spec.model3 && <div className="text-sm text-black font-medium">{spec.model3}</div>}
                    {hoveredModel === 'model4' && spec.model4 && <div className="text-sm text-black font-medium">{spec.model4}</div>}
                    {hoveredModel === 'model5' && spec.model5 && <div className="text-sm text-black font-medium">{spec.model5}</div>}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </motion.div>
      </section>

      {/* PDF functionality now opens in a new tab */}
    </>
  );

  // Return PageLayout component with the product specification content inside
  return (
    <PageLayout
      title="KRYKARD EL/ELB Series UPS"
      subtitle="Reliable power solutions for uninterrupted performance"
      category="protect"
      image="/background_images/ups_layout.png"
    >
      <ProductSpecContent />
    </PageLayout>
  );
};

export default ProductSpecification;