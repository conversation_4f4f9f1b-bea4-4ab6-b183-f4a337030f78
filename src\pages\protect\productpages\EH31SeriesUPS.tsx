import React, { useState, useEffect } from 'react';
import { Check, ArrowUpRight, Award, Zap, Shield, Clock, BarChart3, ArrowR<PERSON>, FileText } from 'lucide-react';
import PageLayout from "@/components/layout/PageLayout";
import { motion } from 'framer-motion';

const ProductSpecification = () => {
  // Mobile responsive table styles
  const customStyles = `
    .specs-table-container {
      position: relative;
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
      scrollbar-width: thin;
      scrollbar-color: #000000 #f1f1f1;
      border-radius: 12px;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .specs-table-container::-webkit-scrollbar {
      height: 8px;
    }

    .specs-table-container::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    .specs-table-container::-webkit-scrollbar-thumb {
      background: #000000;
      border-radius: 4px;
    }

    .specs-table-container::-webkit-scrollbar-thumb:hover {
      background: #333333;
    }

    .specs-table-scroll {
      position: relative;
    }

    .specs-table-scroll::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 20px;
      height: 100%;
      background: linear-gradient(to left, rgba(255,255,255,0.8), transparent);
      pointer-events: none;
      z-index: 1;
    }

    .specs-table {
      width: 100%;
      min-width: 600px;
      border-collapse: collapse;
      background: white;
    }

    .sticky-header-mobile {
      position: sticky;
      left: 0;
      background: white;
      z-index: 2;
      box-shadow: 2px 0 4px rgba(0,0,0,0.1);
    }

    .sticky-cell-mobile {
      position: sticky;
      left: 0;
      background: inherit;
      z-index: 1;
      box-shadow: 2px 0 4px rgba(0,0,0,0.05);
    }

    @media (max-width: 768px) {
      .specs-table {
        min-width: 700px;
      }

      .specs-table th,
      .specs-table td {
        padding: 12px 8px;
        font-size: 14px;
      }

      .sticky-header-mobile,
      .sticky-cell-mobile {
        min-width: 140px;
        max-width: 140px;
      }
    }

    @media (max-width: 640px) {
      .specs-table {
        min-width: 650px;
      }

      .specs-table th,
      .specs-table td {
        padding: 10px 6px;
        font-size: 13px;
      }

      .sticky-header-mobile,
      .sticky-cell-mobile {
        min-width: 120px;
        max-width: 120px;
      }
    }

    .mobile-scroll-indicator {
      display: none;
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      pointer-events: none;
      z-index: 3;
    }

    @media (max-width: 768px) {
      .mobile-scroll-indicator {
        display: block;
      }
    }

    .scroll-hint-arrow {
      animation: bounceRight 2s infinite;
    }

    @keyframes bounceRight {
      0%, 20%, 50%, 80%, 100% {
        transform: translateX(0);
      }
      40% {
        transform: translateX(5px);
      }
      60% {
        transform: translateX(3px);
      }
    }

    .mobile-swipe-instruction {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border: 1px solid #dee2e6;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 16px;
      text-align: center;
      font-weight: 500;
      color: #000000;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    @media (min-width: 769px) {
      .mobile-swipe-instruction {
        display: none;
      }
    }

    .table-responsive-wrapper {
      position: relative;
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .gradient-overlay-left {
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 20px;
      background: linear-gradient(to right, rgba(255,255,255,0.9), transparent);
      pointer-events: none;
      z-index: 2;
    }

    .gradient-overlay-right {
      position: absolute;
      right: 0;
      top: 0;
      bottom: 0;
      width: 20px;
      background: linear-gradient(to left, rgba(255,255,255,0.9), transparent);
      pointer-events: none;
      z-index: 2;
    }
  `;

  // Inject styles into document head
  useEffect(() => {
    const styleElement = document.createElement('style');
    styleElement.textContent = customStyles;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);
  const [activeTab, setActiveTab] = useState('features');
  const [hoveredModel, setHoveredModel] = useState(null);

  const tabs = [
    { id: 'features', label: 'Features' },
    { id: 'advantages', label: 'Advantages' },
    { id: 'benefits', label: 'Benefits' }
  ];

  const featuresList = [
    { title: 'Wide input voltage range (120 - 480 VAC)', desc: 'Protects against unstable input and extends battery life' },
    { title: 'Compact footprint', desc: 'Smallest design for single and three phase UPS' },
    { title: 'Front access maintenance', desc: 'Easier installation and service' },
    { title: 'Frequency range (45 - 55 Hz)', desc: 'Immune to unstable sources' },
    { title: 'Dual feed capability', desc: 'Provides redundant configuration' },
    { title: 'Parallel capability', desc: 'Ideal for high-tier load applications' },
    { title: 'Super powerful DSP microcontroller', desc: 'Best controller with a faster process' },
    { title: 'Full digital control', desc: 'For highest performance' },
    { title: 'DC voltage and current limitation', desc: 'Latest techniques for full protection' },
    { title: 'Overload capability', desc: 'Built-in overload and short circuit protection' },
    { title: 'Control with HMI touchpad', desc: 'Quick and simple access to configuration' },
    { title: 'Transformerless Design', desc: 'Optimizes the Power Factor to 0.9 or higher' },
    { title: 'Higher Power Factor Correction (0.99)', desc: 'Up to 0.99' },
    { title: '≥95% Efficiency' }
  ];

  const advantagesList = [
    { title: 'Maintenance Bypass Switch (optional)', desc: 'Inbuilt Battery Cabinet' },
    { title: 'Current Generator Overload due to starting inrush currents', desc: 'Sensitive medical equipment' },
    { title: 'On-line double conversion & full Digital Frequency Converter', desc: 'Lower total harmonic distortion (< 3%)' },
    { title: 'Built-in system protection diagnostic', desc: 'SNMP / USB Option compatibility' },
    { title: 'Advance backfeed protection circuit design', desc: 'Various operating modes' },
    { title: 'Power operation function', desc: 'Higher efficiency (up to 98% in ECO mode)' },
    { title: 'Overvoltage protection (optional)', desc: 'Including voltage sensor under voltage load' },
    { title: 'Automatic bypass', desc: 'Bypass for fault clearing' },
    { title: 'Built-in DC fuses', desc: 'Automatic battery protection available in Power Vision' },
    { title: '0% to 100% step load change without transfer to Bypass', desc: 'Ideal for powerhungry for redundancy & load changes' },
    { title: 'Multifunction LCD Display', desc: 'Different languages (user-selectable)' },
    { title: 'Robust construction', desc: 'Compact size with maximum performance' },
    { title: 'Low Operating Cost', desc: 'High Efficiency - Upto 95% or better' },
    { title: 'Reduction in carbon footprint', desc: 'Full UPS capacity' },
    { title: 'Increase in IT floor space', desc: 'No transformer required' },
    { title: 'Better ability to match to load', desc: 'Savings of up to 40%' },
    { title: 'Reduction floor space' }
  ];

  const benefitsList = [
    {
      title: 'High Uptime / Availability',
      desc: 'Ensures your critical systems remain operational with minimal to zero interruption, delivering maximum operational continuity.'
    },
    {
      title: 'High Flexibility',
      desc: 'Adapts to various configurations and settings, supporting a wide range of devices and operational environments.'
    },
    {
      title: 'High Reliability',
      desc: 'Engineered with premium components and advanced protection systems to ensure consistent and dependable performance.'
    },
    {
      title: 'Low Total Cost of Ownership (TCO)',
      desc: 'Combines energy efficiency, extended lifespan, and reduced maintenance requirements to minimize long-term costs.'
    }
  ];

  const specifications = [
    { category: 'MODEL', model1: 'EH 31 - 10', model2: 'EH 31 - 20' },
    { category: 'Rated Capacity', model1: '10 kVA / 10 kW', model2: '20 kVA / 20 kW' },
    { category: 'INPUT', model1: '', model2: '' },
    { category: 'Phase', model1: 'Three Phase', model2: 'Three Phase' },
    { category: 'Rated Voltage', model1: '380 / 400 / 415 VAC (three phase)', model2: '380 / 400 / 415 VAC (three phase)' },
    { category: 'Voltage Range', model1: '-40% to +20% (220~476V@100%, 120~220V@70% load)', model2: '-40% to +20% (220~476V@100%, 120~220V@70% load)' },
    { category: 'Rated Frequency', model1: '50 / 60 Hz (auto-sensing)', model2: '50 / 60 Hz (auto-sensing)' },
    { category: 'Frequency Range', model1: '45 ~ 55 Hz', model2: '45 ~ 55 Hz' },
    { category: 'Power Factor', model1: '≥ 0.99', model2: '≥ 0.99' },
    { category: 'Bypass Voltage Range', model1: '-40% ~ +15% (adjustable)', model2: '-40% ~ +15% (adjustable)' },
    { category: 'Current Harmonic Distortion (THDi)', model1: '< 3%', model2: '< 3%' },
    { category: 'OUTPUT', model1: '', model2: '' },
    { category: 'Phase', model1: 'Single phase', model2: 'Single phase' },
    { category: 'Output Wiring', model1: 'Single phase three wires (Ph + N + PE)', model2: 'Single phase three wires (Ph + N + PE)' },
    { category: 'Rated Voltage', model1: '220 / 230 / 240 VAC', model2: '220 / 230 / 240 VAC' },
    { category: 'Voltage Regulation', model1: '±1%', model2: '±1%' },
    { category: 'Waveform', model1: 'Sinusoidal (THD < 3% linear or < 5% non-linear load)', model2: 'Sinusoidal (THD < 3% linear or < 5% non-linear load)' },
    { category: 'Power Factor', model1: '1.0', model2: '1.0' },
    { category: 'Voltage Harmonic Distortion (THDv)', model1: '< 1% (linear load), < 5% (non-linear load)', model2: '< 1% (linear load), < 5% (non-linear load)' },
    { category: 'Crest Factor', model1: '3:1', model2: '3:1' },
    { category: 'Overload', model1: '100% ~ 110% for 10 min, 110% ~ 130% for 1 min, >130% for 3s', model2: '100% ~ 110% for 10 min, 110% ~ 130% for 1 min, >130% for 3s' },
    { category: 'Efficiency', model1: '≥ 93%', model2: '≥ 93%' },
    { category: 'DC VOLTAGE', model1: '192 VDC (16x 12V sealed)', model2: '192 VDC (16x 12V sealed)' },
    { category: 'BATTERY', model1: '', model2: '' },
    { category: 'Charging Time (Typical)', model1: '7 hours to 90% capacity', model2: '7 hours to 90% capacity' },
    { category: 'Backup Time', model1: '8 hrs (depends on capacity of battery)', model2: '8 hrs (depends on capacity of battery)' },
    { category: 'DISPLAY', model1: 'LCD + LED, showing input/output voltage & frequency, load level, battery level & UPS status', model2: 'LCD + LED, showing input/output voltage & frequency, load level, battery level & UPS status' },
    { category: 'Protection', model1: 'Overload, Over Temp, Short circuit, Battery low voltage, Over voltage, Under voltage & Fan failure', model2: 'Overload, Over Temp, Short circuit, Battery low voltage, Over voltage, Under voltage & Fan failure' },
    { category: 'Max. no. of parallel connections', model1: '4', model2: '4' },
    { category: 'Communication', model1: 'Standard configuration: RS-232, USB, EPO, Intelligent slot; Optional: SNMP card, AS400 card, Dry contact, battery temperature sensor', model2: 'Standard configuration: RS-232, USB, EPO, Intelligent slot; Optional: SNMP card, AS400 card, Dry contact, battery temperature sensor' },
    { category: 'Alarms', model1: 'LED / LCD', model2: 'LED / LCD' },
    { category: 'Noise Level (at 1 meter)', model1: '< 55 dB', model2: '< 55 dB' },
    { category: 'Operating Temperature', model1: '0°C ~ 40°C', model2: '0°C ~ 40°C' },
    { category: 'Storage Temperature', model1: '-25°C ~ 55°C (without battery)', model2: '-25°C ~ 55°C (without battery)' },
    { category: 'Relative Humidity', model1: '0 ~ 95% (non-condensing)', model2: '0 ~ 95% (non-condensing)' },
    { category: 'Altitude', model1: '< 1000m, derate 1% per 100m between 1000m-3000m', model2: '< 1000m, derate 1% per 100m between 1000m-3000m' },
    { category: 'PHYSICAL', model1: '', model2: '' },
    { category: 'Tower Model (W × D × H)', model1: '250 × 660 × 550', model2: '250 × 890 × 715' },
    { category: 'Dimensions (mm)', model1: '', model2: '' },
    { category: 'Net Weight (kg)', model1: '75', model2: '120' }
  ];

  const renderContent = () => {
    switch(activeTab) {
      case 'features':
        return (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 md:gap-6">
            {featuresList.map((feature, index) => {
              // Define different gradient colors for variety
              const gradients = [
                "from-blue-600 to-blue-700",
                "from-blue-500 to-blue-600",
                "from-blue-700 to-blue-800",
                "from-indigo-600 to-blue-700",
                "from-blue-600 to-indigo-600"
              ];

              const gradient = gradients[index % gradients.length];

              return (
                <motion.div
                  key={index}
                  className="group bg-white p-4 md:p-6 rounded-xl border border-blue-100 shadow-md hover:shadow-xl transition-all duration-300 relative overflow-hidden"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    type: "spring",
                    stiffness: 100,
                    damping: 12,
                    delay: index * 0.05
                  }}
                  whileHover={{
                    y: -5,
                    transition: { type: "spring", stiffness: 400, damping: 10 }
                  }}
                >
                  {/* Decorative gradient accent */}
                  <div className={`absolute top-0 left-0 w-full h-2 bg-gradient-to-r ${gradient}`}></div>

                  {/* Glow effect on hover */}
                  <motion.div
                    className={`absolute -inset-0.5 bg-gradient-to-r ${gradient} opacity-0 blur-md rounded-xl`}
                    initial={{ opacity: 0 }}
                    whileHover={{ opacity: 0.15 }}
                    transition={{ duration: 0.2 }}
                  />

                  <div className="flex flex-col items-center text-center relative z-10">
                    <motion.div
                      className="mb-4 p-3 rounded-full bg-gray-100 text-black shadow-sm"
                      whileHover={{
                        scale: 1.1,
                        rotate: 5,
                        transition: { type: "spring", stiffness: 400 }
                      }}
                    >
                      <Check size={22} className="text-black" />
                    </motion.div>

                    <h4 className="font-bold text-lg text-black mb-3 break-words">{feature.title}</h4>

                    {feature.desc && (
                      <p className="text-black text-sm">
                        {feature.desc}
                      </p>
                    )}
                  </div>
                </motion.div>
              );
            })}
          </div>
        );

      case 'advantages':
        return (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-8">
            {advantagesList.map((advantage, index) => (
              <motion.div
                key={index}
                className="bg-gradient-to-br from-white to-blue-50 p-4 md:p-6 rounded-xl border border-blue-100 shadow-md hover:shadow-xl transition-all duration-300 relative overflow-hidden"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{
                  type: "spring",
                  stiffness: 100,
                  damping: 12,
                  delay: index * 0.05
                }}
                whileHover={{
                  y: -5,
                  boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
                }}
              >
                {/* Animated background element */}
                <motion.div
                  className="absolute -bottom-6 -right-6 w-32 h-32 bg-blue-100 rounded-full opacity-30"
                  animate={{
                    scale: [1, 1.1, 1],
                    rotate: [0, 5, 0],
                    opacity: [0.2, 0.3, 0.2]
                  }}
                  transition={{
                    duration: 8,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                />

                <div className="flex flex-col sm:flex-row items-start gap-3 md:gap-5 relative z-10">
                  <div className="flex-shrink-0 mt-1 mb-2 sm:mb-0">
                    <motion.div
                      className="w-10 h-10 md:w-12 md:h-12 flex items-center justify-center bg-gradient-to-br from-blue-600 to-blue-700 text-white rounded-lg shadow-md"
                      whileHover={{
                        scale: 1.1,
                        rotate: 5
                      }}
                      animate={{
                        boxShadow: [
                          "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                          "0 6px 10px -1px rgba(0, 0, 0, 0.15)",
                          "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
                        ]
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        repeatType: "reverse"
                      }}
                    >
                      <ArrowUpRight size={20} />
                    </motion.div>
                  </div>

                  <div className="flex-1">
                    <h4 className="font-bold text-lg text-black mb-2 break-words">{advantage.title}</h4>
                    {advantage.desc && (
                      <p className="text-black text-sm md:text-base">
                        {advantage.desc}
                      </p>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        );

      case 'benefits':
        return (
          <div>
            {/* Introduction text */}
            <motion.div
              className="text-center mb-8"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <p className="text-black max-w-3xl mx-auto">
                The EH 31 Series UPS delivers exceptional value through these key benefits, ensuring your critical systems remain protected and operational.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-10">
              {benefitsList.map((benefit, index) => (
                <motion.div
                  key={index}
                  className="bg-white p-5 md:p-8 rounded-2xl border border-blue-100 shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    type: "spring",
                    stiffness: 100,
                    damping: 12,
                    delay: index * 0.1
                  }}
                  whileHover={{
                    y: -5,
                    transition: { type: "spring", stiffness: 400, damping: 10 }
                  }}
                >
                  {/* Decorative elements */}
                  <div className="absolute top-0 right-0 w-40 h-40 bg-blue-50 opacity-40 rounded-full transform translate-x-20 -translate-y-20 z-0"></div>
                  <div className="absolute bottom-0 left-0 w-24 h-24 bg-indigo-50 opacity-40 rounded-full transform -translate-x-10 translate-y-10 z-0"></div>

                  {/* Animated gradient border on hover */}
                  <motion.div
                    className="absolute inset-0 rounded-2xl opacity-0 z-0"
                    initial={{ opacity: 0 }}
                    whileHover={{
                      opacity: 1,
                      transition: { duration: 0.3 }
                    }}
                    style={{
                      background: "linear-gradient(45deg, rgba(37, 99, 235, 0.1), rgba(59, 130, 246, 0.1), rgba(37, 99, 235, 0.1))",
                      backgroundSize: "200% 200%"
                    }}
                    animate={{
                      backgroundPosition: ["0% 0%", "100% 100%", "0% 0%"]
                    }}
                    transition={{
                      duration: 5,
                      repeat: Infinity,
                      repeatType: "mirror"
                    }}
                  />

                  <div className="relative z-10">
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-5 mb-4 md:mb-6">
                      <motion.div
                        className="flex-shrink-0 w-12 h-12 md:w-14 md:h-14 flex items-center justify-center bg-gradient-to-br from-blue-600 to-blue-700 text-white rounded-xl shadow-md mb-2 sm:mb-0"
                        whileHover={{
                          scale: 1.1,
                          rotate: 5
                        }}
                        animate={{
                          boxShadow: [
                            "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                            "0 10px 15px -3px rgba(0, 0, 0, 0.15)",
                            "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
                          ]
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          repeatType: "reverse"
                        }}
                      >
                        <Award size={24} className="md:w-7 md:h-7" />
                      </motion.div>

                      <h3 className="font-bold text-xl md:text-2xl text-black break-words">{benefit.title}</h3>
                    </div>

                    <div className="pl-0 sm:pl-0 md:pl-0 lg:pl-16">
                      <p className="text-black text-sm md:text-base leading-relaxed">
                        {benefit.desc}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  // Stats for Key Features Section
  const keyStats = [
    { value: "95", suffix: "%", title: "Max Efficiency", icon: <Zap size={24} /> },
    { value: "480", suffix: "V", title: "Input Voltage Range", icon: <Shield size={24} /> },
    { value: "1.0", suffix: "", title: "Power Factor", icon: <BarChart3 size={24} /> },
    { value: "4", suffix: "", title: "Parallel Units", icon: <Clock size={24} /> }
  ];

  // PDF URL for brochure
  const pdfUrl = "/Krykard Online UPS January 2025. (1).pdf";

  const ProductSpecContent = () => (
    <div className="w-full mx-auto font-sans">
      {/* Hero Section with Content on Left and Image on Right */}
      <section className="py-16 relative overflow-hidden">
        <div className="relative z-10 px-4 max-w-7xl mx-auto">
          <motion.div
            className="text-black p-8 overflow-hidden relative mb-16"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
          >
            <div className="relative z-10">
              <motion.h1
                className="text-4xl md:text-5xl font-extrabold tracking-tight mb-4 text-black"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.2 }}
              >
                KRYKARD EH 31 SERIES <span className="text-black">3/1 UPS</span>
              </motion.h1>

              <motion.p
                className="text-xl md:text-2xl font-medium mb-8 text-black"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.3 }}
              >
                10 kVA & 20 kVA - Enterprise-grade three-phase input, single-phase output
              </motion.p>

              <motion.div
                className="bg-black text-white font-bold py-4 px-8 rounded-lg inline-block shadow-lg transform hover:scale-105 transition-transform duration-300"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.7, delay: 0.4 }}
              >
                UNMATCHED PERFORMANCE FOR CRITICAL POWER NEEDS
              </motion.div>
            </div>
          </motion.div>

          {/* Hero Content Area - Left content, Right image */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-16">
            {/* Left side: Content */}
            <motion.div
              className="space-y-8"
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <h2 className="text-3xl font-bold text-black mb-4">Enterprise-Grade Power Protection</h2>
                <div className="h-1.5 w-24 bg-black rounded-full mb-6"></div>
                <p className="text-lg text-black leading-relaxed">
                  The KRYKARD EH 31 Series is designed for critical applications requiring three-phase input with single-phase output. Its advanced technology delivers superior protection for your mission-critical equipment.
                </p>
              </motion.div>

              <motion.div
                className="bg-gradient-to-r from-transparent to-blue-50 p-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <h3 className="text-xl font-bold mb-4 text-black">Perfect for:</h3>
                <ul className="space-y-4">
                  {[
                    {icon: "🏭", text: "Industrial Control Systems"},
                    {icon: "🏥", text: "Medical Facilities & Equipment"},
                    {icon: "🏢", text: "Medium to Large Businesses"},
                    {icon: "🖥️", text: "Data Centers & Server Rooms"},
                    {icon: "🔬", text: "Laboratory & Testing Equipment"}
                  ].map((item, index) => (
                    <motion.li
                      key={index}
                      className="flex items-center group"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: 0.4 + index * 0.1 }}
                    >
                      <motion.span
                        className="text-2xl mr-4 transform group-hover:scale-110 transition-transform"
                        animate={{ rotate: [0, 8, 0] }}
                        transition={{ duration: 6, repeat: Infinity, repeatType: "reverse" }}
                      >
                        {item.icon}
                      </motion.span>
                      <span className="text-blue-700 font-medium group-hover:text-blue-600 transition-colors">
                        {item.text}
                      </span>
                    </motion.li>
                  ))}
                </ul>
              </motion.div>

              <motion.div
                className="flex gap-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <motion.a
                  href="/contact/sales"
                  className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-3 rounded-lg shadow-lg flex items-center gap-2 transition-all duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <span>Request Quote</span>
                  <ArrowRight size={18} />
                </motion.a>

                <motion.a
                  href={pdfUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="border-2 border-blue-600 text-blue-700 hover:bg-blue-50 px-6 py-3 rounded-lg shadow-lg transition-all duration-300 flex items-center gap-2"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <FileText size={18} />
                  <span>View Brochure</span>
                </motion.a>
              </motion.div>
            </motion.div>            {/* Right side: UPS Image */}
            <motion.div
              className="relative flex justify-center"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
            >
              <div className="w-full max-w-2xl h-auto md:h-[550px] flex items-center justify-center py-8 md:py-0">
                {/* Enhanced UPS image with up-down animation */}
                <motion.img
                  src="/UPS/5-removebg-preview.png"
                  alt="EH 31 Series UPS Models"
                  className="max-w-full max-h-full object-contain"
                  animate={{
                    y: [0, -20, 0],
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    repeatType: "reverse",
                    ease: "easeInOut"
                  }}
                  whileHover={{ scale: 1.08 }}
                />
              </div>
            </motion.div>
          </div>

          {/* Key Features Section with Enhanced Animations */}
          <div className="mb-20 relative">
            {/* Background decorative elements */}
            <motion.div
              className="absolute -top-20 -left-20 w-64 h-64 bg-blue-50 rounded-full opacity-50 blur-3xl"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.3, 0.5, 0.3],
                rotate: [0, 15, 0]
              }}
              transition={{
                duration: 15,
                repeat: Infinity,
                repeatType: "reverse"
              }}
            />
            <motion.div
              className="absolute -bottom-20 -right-20 w-80 h-80 bg-indigo-50 rounded-full opacity-50 blur-3xl"
              animate={{
                scale: [1, 1.3, 1],
                opacity: [0.3, 0.6, 0.3],
                rotate: [0, -15, 0]
              }}
              transition={{
                duration: 18,
                repeat: Infinity,
                repeatType: "reverse",
                delay: 2
              }}
            />

            <motion.div
              className="text-center mb-16 relative z-10"
              initial={{ opacity: 0, y: -20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
            >
              <motion.div
                initial={{ scale: 0.95, opacity: 0 }}
                whileInView={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <motion.h2
                  className="text-5xl font-bold text-blue-900 mb-4 inline-block relative"
                  animate={{
                    textShadow: [
                      "0 0 0px rgba(30, 64, 175, 0)",
                      "0 0 5px rgba(30, 64, 175, 0.3)",
                      "0 0 0px rgba(30, 64, 175, 0)"
                    ]
                  }}
                  transition={{
                    duration: 5,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                >
                  Key Features
                  <motion.div
                    className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 h-2 bg-gradient-to-r from-blue-400 via-blue-600 to-blue-400 rounded-full"
                    initial={{ width: 0, opacity: 0 }}
                    whileInView={{ width: 120, opacity: 1 }}
                    transition={{ duration: 1, delay: 0.3 }}
                    viewport={{ once: true }}
                  />
                </motion.h2>
              </motion.div>
              <motion.p
                className="mt-6 text-xl text-blue-700 max-w-2xl mx-auto font-medium"
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                viewport={{ once: true }}
              >
                Advanced capabilities that define our enterprise-grade 3/1 UPS solutions
              </motion.p>
            </motion.div>

            {/* Modern 3D Card Layout with Enhanced Professional Design and Animations */}
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 md:gap-8 relative z-10">
              {keyStats.map((stat, index) => {
                // Define different gradient colors for each card
                const gradients = [
                  "from-blue-600 to-blue-800",
                  "from-indigo-600 to-blue-700",
                  "from-blue-700 to-indigo-700",
                  "from-blue-700 to-blue-900"
                ];

                // Define different glow effects for each card
                const glows = [
                  "from-blue-500/30 via-blue-600/20 to-transparent",
                  "from-indigo-500/30 via-blue-600/20 to-transparent",
                  "from-blue-600/30 via-indigo-600/20 to-transparent",
                  "from-blue-700/30 via-blue-800/20 to-transparent"
                ];

                return (
                  <motion.div
                    key={index}
                    className="relative group"
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{
                      type: "spring",
                      stiffness: 100,
                      damping: 12,
                      delay: index * 0.15
                    }}
                    viewport={{ once: true }}
                    whileHover={{
                      y: -8,
                      transition: { type: "spring", stiffness: 400, damping: 10 }
                    }}
                  >
                    {/* Card background with gradient */}
                    <motion.div
                      className={`absolute inset-0 rounded-xl bg-gradient-to-br ${gradients[index]} transform transition-all duration-300`}
                      whileHover={{ scale: 1.03 }}
                      animate={{
                        backgroundPosition: ['0% 0%', '100% 100%'],
                        transition: {
                          duration: 8,
                          repeat: Infinity,
                          repeatType: "reverse"
                        }
                      }}
                    ></motion.div>

                    {/* Glow effect */}
                    <motion.div
                      className={`absolute -inset-0.5 rounded-xl bg-gradient-to-br ${glows[index]} opacity-0 blur-md`}
                      whileHover={{ opacity: 1 }}
                      transition={{ duration: 0.3 }}
                      animate={{
                        boxShadow: [
                          '0 0 0px rgba(59, 130, 246, 0)',
                          '0 0 20px rgba(59, 130, 246, 0.3)',
                          '0 0 0px rgba(59, 130, 246, 0)'
                        ],
                        transition: {
                          duration: 4,
                          repeat: Infinity,
                          repeatType: "reverse"
                        }
                      }}
                    ></motion.div>

                    {/* Card content */}
                    <div className="relative bg-white rounded-xl p-4 md:p-8 h-full border border-blue-100 shadow-lg group-hover:shadow-2xl transition-all duration-300">
                      <div className="flex flex-col items-center text-center h-full">
                        <motion.div
                          className="mb-3 md:mb-5 p-3 md:p-4 rounded-full bg-gradient-to-br from-blue-50 to-blue-100 shadow-inner"
                          whileHover={{
                            rotate: 360,
                            scale: 1.1,
                            transition: { duration: 0.5 }
                          }}
                          animate={{
                            boxShadow: [
                              'inset 0 2px 4px 0 rgba(0, 0, 0, 0.05)',
                              'inset 0 4px 8px 0 rgba(0, 0, 0, 0.1)',
                              'inset 0 2px 4px 0 rgba(0, 0, 0, 0.05)'
                            ]
                          }}
                          transition={{
                            duration: 3,
                            repeat: Infinity,
                            repeatType: "reverse"
                          }}
                        >
                          <motion.div
                            animate={{
                              scale: [1, 1.1, 1],
                              color: [
                                'rgb(29, 78, 216)', // blue-700
                                'rgb(67, 56, 202)', // indigo-700
                                'rgb(29, 78, 216)'  // blue-700
                              ]
                            }}
                            transition={{
                              duration: 4,
                              repeat: Infinity,
                              repeatType: "reverse"
                            }}
                          >
                            {React.cloneElement(stat.icon, { className: "", size: 24 })}
                          </motion.div>
                        </motion.div>

                        <motion.div
                          className="mb-2 md:mb-3"
                          initial={{ opacity: 0, scale: 0.8 }}
                          whileInView={{ opacity: 1, scale: 1 }}
                          transition={{
                            duration: 0.5,
                            delay: 0.3 + index * 0.1,
                            type: "spring"
                          }}
                          viewport={{ once: true }}
                        >
                          <motion.span
                            className="text-3xl md:text-5xl font-extrabold bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-blue-900"
                            animate={{
                              backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
                            }}
                            transition={{
                              duration: 5,
                              repeat: Infinity,
                              repeatType: "reverse"
                            }}
                          >
                            {stat.value}
                          </motion.span>
                          <motion.span
                            className="text-xl md:text-2xl font-bold text-blue-700"
                            animate={{
                              y: [0, -3, 0]
                            }}
                            transition={{
                              duration: 2,
                              repeat: Infinity,
                              repeatType: "reverse",
                              delay: 1
                            }}
                          >
                            {stat.suffix}
                          </motion.span>
                        </motion.div>

                        <motion.h3
                          className="text-base md:text-xl font-bold text-blue-800 mb-2 break-words"
                          initial={{ opacity: 0 }}
                          whileInView={{ opacity: 1 }}
                          transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
                          viewport={{ once: true }}
                          whileHover={{
                            textShadow: "0 0 8px rgba(30, 64, 175, 0.3)"
                          }}
                        >
                          {stat.title}
                        </motion.h3>

                        <motion.div
                          className="h-0.5 bg-gradient-to-r from-transparent via-blue-300 to-transparent mx-auto mb-2"
                          initial={{ width: 0 }}
                          whileInView={{ width: 60 }}
                          viewport={{ once: true }}
                          animate={{
                            opacity: [0.5, 1, 0.5]
                          }}
                          transition={{
                            duration: 3,
                            repeat: Infinity,
                            repeatType: "reverse",
                            delay: 0.5 + index * 0.1
                          }}
                        />
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </div>
      </section>

      {/* Tabs Section with Enhanced Modern Design */}
      <section className="max-w-7xl mx-auto px-4 mb-20">
        {/* Section Title with Animation */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true }}
        >
          <motion.h2
            className="text-4xl font-bold text-blue-900 mb-3 inline-block relative"
            animate={{
              textShadow: [
                "0 0 0px rgba(30, 64, 175, 0)",
                "0 0 5px rgba(30, 64, 175, 0.2)",
                "0 0 0px rgba(30, 64, 175, 0)"
              ]
            }}
            transition={{
              duration: 5,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          >
            Product Highlights
            <motion.div
              className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-blue-400 via-blue-600 to-blue-400"
              initial={{ scaleX: 0, opacity: 0 }}
              whileInView={{ scaleX: 1, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
            />
          </motion.h2>
          <motion.p
            className="mt-4 text-lg text-blue-700 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 10 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            viewport={{ once: true }}
          >
            Explore the comprehensive features, advantages, and benefits of our EH 31 Series UPS
          </motion.p>
        </motion.div>

        {/* Modern Tab Buttons with Enhanced Design */}
        <div className="flex flex-wrap justify-center mb-12 relative">
          {/* Background blur effect */}
          <motion.div
            className="absolute inset-0 bg-blue-50/30 blur-xl rounded-full"
            animate={{
              opacity: [0.3, 0.5, 0.3],
              scale: [0.95, 1.05, 0.95]
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />

          {/* Tab buttons */}
          <div className="relative z-10 bg-white/80 backdrop-blur-sm rounded-full p-2 shadow-lg border border-blue-100 flex">
            {tabs.map(tab => (
              <motion.button
                key={tab.id}
                className={`relative py-3 px-10 font-medium text-lg transition-all duration-300 rounded-full ${
                  activeTab === tab.id
                    ? 'text-white shadow-md'
                    : 'text-blue-700 hover:text-blue-900'
                }`}
                onClick={() => setActiveTab(tab.id)}
                whileHover={{ scale: activeTab === tab.id ? 1 : 1.05 }}
                whileTap={{ scale: 0.98 }}
              >
                {activeTab === tab.id && (
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full"
                    layoutId="activeTabBackground"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.3 }}
                  />
                )}
                <span className="relative z-10">{tab.label}</span>
              </motion.button>
            ))}
          </div>
        </div>

        {/* Enhanced Tab Content Container with Modern Animation */}
        <motion.div
          className="p-10 bg-white rounded-3xl shadow-xl border border-blue-100 overflow-hidden relative"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.3 }}
          viewport={{ once: true }}
        >
          {/* Animated decorative elements */}
          <motion.div
            className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-full opacity-50 blur-3xl"
            animate={{
              scale: [1, 1.2, 1],
              x: [20, 30, 20],
              y: [-20, -30, -20],
              opacity: [0.3, 0.5, 0.3]
            }}
            transition={{
              duration: 15,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
          <motion.div
            className="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-tr from-indigo-50 to-blue-100/50 rounded-full opacity-50 blur-3xl"
            animate={{
              scale: [1, 1.3, 1],
              x: [-20, -30, -20],
              y: [20, 30, 20],
              opacity: [0.3, 0.6, 0.3]
            }}
            transition={{
              duration: 18,
              repeat: Infinity,
              repeatType: "reverse",
              delay: 2
            }}
          />

          {/* Tab content with animation */}
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3 }}
            className="relative z-10"
          >
            {renderContent()}
          </motion.div>
        </motion.div>
      </section>

      {/* Modern Technical Specifications Section */}
      <section className="max-w-7xl mx-auto px-4 mb-20 relative">
        {/* Background decorative elements */}
        <motion.div
          className="absolute -top-40 -right-40 w-96 h-96 bg-blue-50/50 rounded-full opacity-60 blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.4, 0.6, 0.4],
            rotate: [0, 15, 0]
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        />
        <motion.div
          className="absolute -bottom-40 -left-40 w-96 h-96 bg-indigo-50/50 rounded-full opacity-60 blur-3xl"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.4, 0.7, 0.4],
            rotate: [0, -15, 0]
          }}
          transition={{
            duration: 18,
            repeat: Infinity,
            repeatType: "reverse",
            delay: 2
          }}
        />

        {/* Section Header with Enhanced Animation */}
        <motion.div
          className="text-center mb-12 relative z-10"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true }}
        >
          <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.h2
              className="text-4xl font-bold text-blue-950 mb-4 inline-block relative"
              animate={{
                textShadow: [
                  "0 0 0px rgba(30, 64, 175, 0)",
                  "0 0 5px rgba(30, 64, 175, 0.3)",
                  "0 0 0px rgba(30, 64, 175, 0)"
                ]
              }}
              transition={{
                duration: 5,
                repeat: Infinity,
                repeatType: "reverse"
              }}
            >
              Technical Specifications
              <motion.div
                className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 h-1.5 w-32 bg-gradient-to-r from-blue-500 via-blue-600 to-blue-500 rounded-full"
                initial={{ width: 0, opacity: 0 }}
                whileInView={{ width: 180, opacity: 1 }}
                transition={{ duration: 1, delay: 0.3 }}
                viewport={{ once: true }}
              />
            </motion.h2>
          </motion.div>
          <motion.p
            className="mt-6 text-xl text-blue-900 max-w-2xl mx-auto font-medium"
            initial={{ opacity: 0, y: 10 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            Comprehensive technical details for the EH 31 Series UPS line
          </motion.p>
        </motion.div>

        {/* Modern Interactive Table with Enhanced Design */}
        <motion.div
          className="bg-white rounded-2xl shadow-xl overflow-hidden border border-blue-100 relative z-10"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true }}
          whileHover={{ boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.1)" }}
        >
          {/* Subtle gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 to-transparent pointer-events-none"></div>

          {/* Table container with improved styling */}
          <div className="table-responsive-wrapper">
            <div className="mobile-swipe-instruction">
              📱 Swipe left to see more specifications →
            </div>
            <div className="mobile-scroll-indicator">
              <span className="scroll-hint-arrow">→</span> Scroll
            </div>
            <div className="gradient-overlay-left"></div>
            <div className="gradient-overlay-right"></div>
            <div className="specs-table-container">
              <div className="specs-table-scroll">
                <table className="specs-table">
                <thead>
                  <tr className="bg-gradient-to-r from-black to-gray-800 text-white">
                    <th className="sticky-header-mobile py-3 md:py-5 px-3 md:px-6 text-left font-bold text-sm md:text-lg uppercase tracking-wider">
                      <span className="flex items-center">
                        <span>Specifications</span>
                      </span>
                    </th>
                    {specifications[0].model1 &&
                      <th className="py-3 md:py-5 px-3 md:px-6 text-left font-bold text-sm md:text-lg uppercase tracking-wider relative">
                        <div
                          className={`absolute inset-0 bg-blue-800 opacity-0 transition-opacity duration-300 ${hoveredModel === 'model1' ? 'opacity-30' : ''}`}
                        />
                        <div
                          className="relative z-10 flex items-center"
                          onMouseEnter={() => setHoveredModel('model1')}
                          onMouseLeave={() => setHoveredModel(null)}
                        >
                          {specifications[0].model1}
                        </div>
                      </th>
                    }
                    {specifications[0].model2 &&
                      <th className="py-3 md:py-5 px-3 md:px-6 text-left font-bold text-sm md:text-lg uppercase tracking-wider relative">
                        <div
                          className={`absolute inset-0 bg-blue-800 opacity-0 transition-opacity duration-300 ${hoveredModel === 'model2' ? 'opacity-30' : ''}`}
                        />
                        <div
                          className="relative z-10 flex items-center"
                          onMouseEnter={() => setHoveredModel('model2')}
                          onMouseLeave={() => setHoveredModel(null)}
                        >
                          {specifications[0].model2}
                        </div>
                      </th>
                    }
                  </tr>
                </thead>
                <tbody>
                  {specifications.slice(1).map((spec, index) => {
                    // Determine if this is a category header row
                    const isHeader =
                      spec.category.includes('INPUT') ||
                      spec.category.includes('OUTPUT') ||
                      spec.category.includes('BATTERY') ||
                      spec.category.includes('DISPLAY') ||
                      spec.category.includes('PHYSICAL');

                    return (
                      <motion.tr
                        key={index}
                        className={`border-b border-blue-100 transition-all duration-200 ${
                          isHeader
                            ? 'bg-gradient-to-r from-blue-100 to-blue-50'
                            : index % 2 === 0 ? 'bg-white' : 'bg-blue-50/50'
                        } hover:bg-blue-100/70`}
                        initial={{ opacity: 0, x: -10 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{
                          type: "spring",
                          stiffness: 100,
                          damping: 12,
                          delay: index * 0.01
                        }}
                        viewport={{ once: true, margin: "-100px" }}
                        whileHover={{
                          scale: isHeader ? 1 : 1.005,
                          transition: { duration: 0.2 }
                        }}
                      >
                        <td className={`sticky-cell-mobile py-2 md:py-4 px-3 md:px-6 ${
                          isHeader
                            ? 'font-bold text-black text-sm md:text-base'
                            : 'font-bold text-gray-900 text-xs md:text-sm'
                        }`}>
                          {isHeader ? (
                            <div className="flex items-center">
                              <div className="w-1.5 h-5 bg-black rounded-full mr-2 md:mr-3"></div>
                              {spec.category}
                            </div>
                          ) : (
                            spec.category
                          )}
                        </td>
                        {spec.model1 !== undefined &&
                          <td className={`py-2 md:py-4 px-3 md:px-6 ${
                            isHeader ? 'font-bold text-black' : 'text-gray-900 font-bold'
                          } ${hoveredModel === 'model1' ? 'bg-blue-100/80' : ''} text-xs md:text-sm`}>
                            {spec.model1}
                          </td>
                        }
                        {spec.model2 !== undefined &&
                          <td className={`py-2 md:py-4 px-3 md:px-6 ${
                            isHeader ? 'font-bold text-black' : 'text-gray-900 font-bold'
                          } ${hoveredModel === 'model2' ? 'bg-blue-100/80' : ''} text-xs md:text-sm`}>
                            {spec.model2}
                          </td>
                        }
                      </motion.tr>
                    );
                  })}
                </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* Bottom gradient accent */}
          <div className="h-1.5 w-full bg-black"></div>
        </motion.div>


      </section>

      {/* Key Features Highlight Section */}
      <section className="max-w-7xl mx-auto px-4 mb-20">
        <motion.div
          className="text-center mb-10"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <h2 className="text-3xl font-bold text-black mb-3">Key Highlights</h2>
          <div className="h-1.5 w-32 bg-black mx-auto rounded-full"></div>
          <p className="mt-4 text-lg text-black max-w-2xl mx-auto">
            Standout features that make the EH 31 Series exceptional
          </p>
        </motion.div>

        {/* Enhanced Feature Cards with 3D effects */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 md:gap-8 relative z-10">
          {[
            {
              icon: <Zap size={24} />,
              value: "Unity",
              suffix: "",
              title: "Power Factor",
              description: "1.0 power factor ensures that the kVA rating equals the kW rating, maximizing the efficiency of your power protection investment",
              color: "from-blue-500 to-blue-600",
              bgGlow: "from-blue-400/20 via-blue-500/10 to-transparent"
            },
            {
              icon: <Shield size={24} />,
              value: "120-480",
              suffix: "VAC",
              title: "Input Voltage Range",
              description: "Operates in environments with unstable three-phase power conditions without switching to battery mode, extending battery life",
              color: "from-green-500 to-blue-500",
              bgGlow: "from-green-400/20 via-blue-500/10 to-transparent"
            },
            {
              icon: <Clock size={24} />,
              value: "4",
              suffix: "",
              title: "Parallel Units",
              description: "Connect up to 4 units in parallel to increase capacity or add redundancy to your power infrastructure",
              color: "from-blue-600 to-indigo-600",
              bgGlow: "from-blue-500/20 via-indigo-500/10 to-transparent"
            }
          ].map((feature, index) => (
            <motion.div
              key={index}
              className="relative"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
            >
              {/* Card background with gradient */}
              <div className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${feature.color} transform transition-transform duration-300 group-hover:scale-[1.02]`}></div>

              {/* Glow effect */}
              <div className={`absolute -inset-0.5 rounded-2xl bg-gradient-to-br ${feature.bgGlow} opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300`}></div>

              {/* Card content */}
              <div className="relative bg-white rounded-2xl p-4 md:p-8 h-full border border-blue-100 shadow-md hover:shadow-xl transition-all duration-300">
                <div className="flex flex-col h-full">
                  {/* Icon */}
                  <div className="mb-4 md:mb-6 flex justify-center">
                    <div className="p-3 rounded-full bg-gray-100">
                      {React.cloneElement(feature.icon, { className: "text-black" })}
                    </div>
                  </div>

                  {/* Title and value */}
                  <div className="mb-3 md:mb-4 text-center">
                    <div className="mb-1">
                      <span className="text-2xl md:text-3xl font-extrabold bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-blue-500">{feature.value}</span>
                      <span className="text-lg md:text-xl font-bold text-blue-600">{feature.suffix}</span>
                    </div>
                    <h3 className="text-lg md:text-xl font-bold text-blue-800 break-words">{feature.title}</h3>
                  </div>

                  {/* Description */}
                  <p className="text-blue-700 text-sm md:text-base text-center mt-auto">
                    {feature.description}
                  </p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        <div className="mt-8 md:mt-12 grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
          <motion.div
            className="bg-white p-5 md:p-8 rounded-xl shadow-lg border border-blue-100 relative overflow-hidden"
            whileHover={{ y: -5, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-blue-400 to-blue-600"></div>

            <h3 className="text-lg md:text-xl font-bold text-blue-800 mb-3 md:mb-4">Front Access Maintenance</h3>
            <p className="text-blue-700 text-sm md:text-base mb-4">
              The EH 31 Series features a user-friendly design with front-access maintenance, making installation and service easier and faster. This design reduces downtime and simplifies technical support.
            </p>

            <div className="flex flex-col sm:flex-row items-start sm:items-center sm:justify-between bg-blue-50 p-4 rounded-lg">
              <div className="text-xs md:text-sm text-blue-600 mb-3 sm:mb-0">
                <div className="font-bold">Easy Maintenance</div>
                <div>Front access design</div>
                <div>Simplified service and support</div>
              </div>
              <div className="bg-blue-100 p-2 rounded-full self-center sm:self-auto">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 md:h-8 md:w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="bg-white p-5 md:p-8 rounded-xl shadow-lg border border-blue-100 relative overflow-hidden"
            whileHover={{ y: -5, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-blue-400 to-blue-600"></div>

            <h3 className="text-lg md:text-xl font-bold text-blue-800 mb-3 md:mb-4">Dimensions & Form Factor</h3>
            <p className="text-blue-700 text-sm md:text-base mb-4">
              The EH 31 Series offers a compact footprint compared to traditional UPS systems of similar capacity, making it ideal for environments where space is at a premium while still providing robust power protection.
            </p>

            <div className="flex flex-col sm:flex-row items-start sm:items-center sm:justify-between bg-blue-50 p-4 rounded-lg">
              <div className="text-xs md:text-sm text-blue-600 mb-3 sm:mb-0">
                <div className="font-bold">Dimensions (W×D×H)</div>
                <div>EH 31 - 10: 250 × 660 × 550 mm</div>
                <div>EH 31 - 20: 250 × 890 × 715 mm</div>
              </div>
              <div className="bg-blue-100 p-2 rounded-full self-center sm:self-auto">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 md:h-8 md:w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
                </svg>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Application Areas */}
      <section className="max-w-7xl mx-auto px-4 mb-20">
        <motion.div
          className="text-center mb-10"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold text-blue-900 mb-3 inline-block relative">
              Ideal Applications
              <motion.div
                className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-blue-400 via-blue-600 to-blue-400"
                initial={{ scaleX: 0, opacity: 0 }}
                whileInView={{ scaleX: 1, opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
              />
            </h2>
          </motion.div>
          <p className="mt-4 text-lg text-blue-700 max-w-2xl mx-auto">
            Perfect solutions for these critical environments
          </p>
        </motion.div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
          {[
            { icon: "🏭", text: "Industrial Settings" },
            { icon: "🏥", text: "Medical Environments" },
            { icon: "💻", text: "Data Centers" },
            { icon: "🔌", text: "Telecom Infrastructure" }
          ].map((item, idx) => (
            <motion.div
              key={idx}
              className="bg-blue-50 rounded-xl p-3 md:p-5 shadow-sm hover:shadow-md transition-all duration-300 hover:bg-blue-100/50"
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: idx * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
            >
              <motion.div
                className="text-2xl md:text-3xl mb-2 md:mb-3"
                animate={{
                  y: [0, -5, 0],
                  rotate: [-2, 2, -2]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              >
                {item.icon}
              </motion.div>
              <h3 className="font-bold text-sm md:text-base text-blue-800">{item.text}</h3>
            </motion.div>
          ))}
        </div>

        <div className="mt-8 md:mt-12 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 md:gap-8">
          <motion.div
            className="bg-white p-5 md:p-8 rounded-xl shadow-lg border border-blue-100 flex flex-col items-center text-center"
            whileHover={{ scale: 1.02, y: -5, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.1)" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <div className="w-12 h-12 md:w-16 md:h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-full flex items-center justify-center text-white mb-4 md:mb-6 shadow-lg">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 md:h-8 md:w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <h3 className="text-lg md:text-xl font-bold text-blue-800 mb-3 md:mb-4">Industrial Settings</h3>
            <p className="text-blue-700 text-sm md:text-base">
              Provides reliable three-phase power protection for manufacturing equipment, industrial controls, and automation systems in factories and plants.
            </p>
          </motion.div>

          <motion.div
            className="bg-white p-5 md:p-8 rounded-xl shadow-lg border border-blue-100 flex flex-col items-center text-center"
            whileHover={{ scale: 1.02, y: -5, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.1)" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <div className="w-12 h-12 md:w-16 md:h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-full flex items-center justify-center text-white mb-4 md:mb-6 shadow-lg">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 md:h-8 md:w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
              </svg>
            </div>
            <h3 className="text-lg md:text-xl font-bold text-blue-800 mb-3 md:mb-4">Medical Environments</h3>
            <p className="text-blue-700 text-sm md:text-base">
              Ensures continuous operation of critical medical equipment, diagnostic systems, and healthcare IT infrastructure with clean, stable power.
            </p>
          </motion.div>

          <motion.div
            className="bg-white p-5 md:p-8 rounded-xl shadow-lg border border-blue-100 flex flex-col items-center text-center"
            whileHover={{ scale: 1.02, y: -5, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.1)" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="w-12 h-12 md:w-16 md:h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-full flex items-center justify-center text-white mb-4 md:mb-6 shadow-lg">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 md:h-8 md:w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
              </svg>
            </div>
            <h3 className="text-lg md:text-xl font-bold text-blue-800 mb-3 md:mb-4">Data Centers</h3>
            <p className="text-blue-700 text-sm md:text-base">
              Protects mission-critical servers, networking equipment, and storage systems where three-phase power input is available but single-phase output is needed.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Why Choose Section */}
      <section className="max-w-7xl mx-auto px-4 mb-20">
        <motion.div
          className="text-center mb-12 relative z-10"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold text-blue-900 mb-3 inline-block relative">
              Why Choose EH 31 Series
              <motion.div
                className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-blue-400 via-blue-600 to-blue-400"
                initial={{ scaleX: 0, opacity: 0 }}
                whileInView={{ scaleX: 1, opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
              />
            </h2>
          </motion.div>
          <motion.p
            className="mt-4 text-lg text-blue-700 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 10 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            viewport={{ once: true }}
          >
            The EH 31 Series offers a comprehensive set of features and benefits designed for mission-critical applications
          </motion.p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
          <motion.div
            className="bg-white rounded-xl shadow-xl overflow-hidden"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            whileHover={{ y: -5 }}
          >
            {/* Top gradient bar */}
            <div className="h-2 bg-gradient-to-r from-blue-500 to-blue-700"></div>

            {/* Header */}
            <div className="bg-gradient-to-br from-blue-500 to-blue-700 p-4 md:p-6 relative overflow-hidden">
              {/* Decorative elements */}
              <div className="absolute -top-12 -right-12 w-40 h-40 bg-white opacity-10 rounded-full"></div>
              <div className="absolute bottom-0 left-0 w-full h-12 bg-gradient-to-r from-blue-700/20 to-transparent"></div>

              <h3 className="text-xl md:text-2xl font-bold text-white relative z-10 flex items-center">
                <Shield className="mr-2 md:mr-3" size={20} />
                <span className="break-words">Technical Advantages</span>
              </h3>
            </div>

            {/* Features List */}
            <div className="p-4 md:p-6">
              <ul className="space-y-3 md:space-y-4">
                {[
                  "Three-phase input with single-phase output flexibility",
                  "Wide input voltage range (120-480 VAC) handles unstable power",
                  "DSP microcontroller for superior performance and control",
                  "Unity power factor (1.0) delivers full rated power capacity",
                  "Parallel capability for up to 4 units for expanded capacity"
                ].map((feature, index) => (
                  <motion.li
                    key={index}
                    className="flex items-start"
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 * index }}
                    viewport={{ once: true }}
                  >
                    <motion.div
                      className="mt-1 mr-2 md:mr-3 text-blue-600 bg-blue-100 p-1 rounded-full flex-shrink-0"
                      whileHover={{ scale: 1.2, rotate: 180 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Check size={14} />
                    </motion.div>
                    <span className="text-blue-800 font-medium text-sm md:text-base">{feature}</span>
                  </motion.li>
                ))}
              </ul>
            </div>
          </motion.div>

          <motion.div
            className="bg-white rounded-xl shadow-xl overflow-hidden"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            whileHover={{ y: -5 }}
          >
            {/* Top gradient bar */}
            <div className="h-2 bg-gradient-to-r from-blue-600 to-blue-800"></div>

            {/* Header */}
            <div className="bg-gradient-to-br from-blue-600 to-blue-800 p-4 md:p-6 relative overflow-hidden">
              {/* Decorative elements */}
              <div className="absolute -top-12 -right-12 w-40 h-40 bg-white opacity-10 rounded-full"></div>
              <div className="absolute bottom-0 left-0 w-full h-12 bg-gradient-to-r from-blue-800/20 to-transparent"></div>

              <h3 className="text-xl md:text-2xl font-bold text-white relative z-10 flex items-center">
                <BarChart3 className="mr-2 md:mr-3" size={20} />
                <span className="break-words">Business Benefits</span>
              </h3>
            </div>

            {/* Features List */}
            <div className="p-4 md:p-6">
              <ul className="space-y-3 md:space-y-4">
                {[
                  "Front access maintenance design reduces service time and cost",
                  "High efficiency (≥93%) lowers operational expenses",
                  "Transformerless design reduces size and installation complexity",
                  "Step load capability handles power-hungry equipment startup",
                  "Complete protection features safeguard critical investments"
                ].map((feature, index) => (
                  <motion.li
                    key={index}
                    className="flex items-start"
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 * index }}
                    viewport={{ once: true }}
                  >
                    <motion.div
                      className="mt-1 mr-2 md:mr-3 text-blue-600 bg-blue-100 p-1 rounded-full flex-shrink-0"
                      whileHover={{ scale: 1.2, rotate: 180 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Check size={14} />
                    </motion.div>
                    <span className="text-blue-800 font-medium text-sm md:text-base">{feature}</span>
                  </motion.li>
                ))}
              </ul>
            </div>
          </motion.div>
        </div>
      </section>



      {/* Need More Information Section */}
      <section className="max-w-7xl mx-auto px-4 mb-16 md:mb-20">
        <motion.div
          className="bg-blue-100/80 rounded-xl p-6 md:p-10 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <motion.h2
            className="text-2xl md:text-3xl font-bold text-blue-700 mb-3 md:mb-4"
            initial={{ opacity: 0, y: -10 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            Need More Information?
          </motion.h2>

          <motion.p
            className="text-blue-600 text-sm md:text-base max-w-3xl mx-auto mb-6 md:mb-8"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Our team of experts is ready to help you with product specifications, custom solutions, pricing, and
            any other details you need about the KRYKARD UPS systems.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 10 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <motion.a
              href="/contact/sales"
              className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 md:px-6 py-2 md:py-3 rounded-lg transition-colors duration-300 text-sm md:text-base"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 md:h-5 md:w-5" viewBox="0 0 20 20" fill="currentColor">
                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
              </svg>
              <span>Contact Our Experts</span>
            </motion.a>
          </motion.div>
        </motion.div>
      </section>
    </div>
  );

  // Return PageLayout component with the product specification content inside
  return (
    <PageLayout
      title="KRYKARD EH 31 Series UPS"
      subtitle="Unmatched performance for critical power needs"
      category="protect"
      image="/background_images/ups_layout.png"
    >
      <ProductSpecContent />
    </PageLayout>
  );
};

export default ProductSpecification;