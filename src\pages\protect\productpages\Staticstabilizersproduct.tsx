import { <PERSON>, Zap, <PERSON>ting<PERSON>, Award, CheckCircle, Star, Mail, Download, Info } from 'lucide-react';
import PageLayout from "@/components/layout/PageLayout";
import { useEffect } from 'react';

const StaticStabilizersProduct = () => {
  // Mobile table CSS injection
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .specs-table-container {
        width: 100%;
        margin: 0;
        padding: 0;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .specs-table-scroll {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: thin;
        scrollbar-color: #374151 #f3f4f6;
      }

      .specs-table-scroll::-webkit-scrollbar {
        height: 8px;
      }

      .specs-table-scroll::-webkit-scrollbar-track {
        background: #f3f4f6;
        border-radius: 4px;
      }

      .specs-table-scroll::-webkit-scrollbar-thumb {
        background: #374151;
        border-radius: 4px;
      }

      .specs-table-scroll::-webkit-scrollbar-thumb:hover {
        background: #1f2937;
      }

      .specs-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 14px;
        min-width: 600px;
      }

      .specs-table th {
        background: #000;
        color: white;
        padding: 12px 16px;
        text-align: left;
        font-weight: 600;
        font-size: 14px;
        border-bottom: 2px solid #374151;
        white-space: nowrap;
        position: sticky;
        top: 0;
        z-index: 10;
      }

      .specs-table th:first-child {
        position: sticky;
        left: 0;
        z-index: 11;
        min-width: 200px;
      }

      .specs-table td {
        padding: 10px 16px;
        border-bottom: 1px solid #e5e7eb;
        font-size: 13px;
        line-height: 1.4;
        color: #374151;
      }

      .specs-table td:first-child {
        background: #f9fafb;
        font-weight: 600;
        color: #000;
        position: sticky;
        left: 0;
        z-index: 9;
        border-right: 1px solid #e5e7eb;
        min-width: 200px;
      }

      .specs-table tbody tr:hover {
        background-color: #f3f4f6;
      }

      .specs-table tbody tr:hover td:first-child {
        background-color: #e5e7eb;
      }

      @media (max-width: 768px) {
        .specs-table {
          font-size: 12px;
          min-width: 500px;
        }

        .specs-table th {
          padding: 8px 12px;
          font-size: 12px;
        }

        .specs-table td {
          padding: 8px 12px;
          font-size: 11px;
        }

        .specs-table th:first-child,
        .specs-table td:first-child {
          min-width: 150px;
        }
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const features = [
    {
      icon: <Shield className="w-8 h-8" />,
      title: "Wide Product Range",
      items: [
        "Ratings 15 - 200 kVA 3 Phase",
        "Multiple input ranges to suit various operating environments",
        "LS Series: 15 - 100 kVA",
        "HS Series: 125 - 200 kVA"
      ]
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: "Fast Voltage Correction",
      items: [
        "Any variation within input range is corrected within 20 ms",
        "Steady Output Voltage - No Hunting or Overshoot",
        "True RMS Sensing and Correction",
        "High Speed 20 kHz PWM switching using DSP technology"
      ]
    },
    {
      icon: <Settings className="w-8 h-8" />,
      title: "Complete Protection",
      items: [
        "Short Circuit, Under voltage, Over voltage & Overload trip",
        "Single Phasing & Phase Reversal trip",
        "Spike & Surge Protection",
        "Service & Manual Bypass provision"
      ]
    },
    {
      icon: <Award className="w-8 h-8" />,
      title: "IGBT Technology",
      items: [
        "Fully Solid State - IGBT Technology",
        "Lower output load supply impedance",
        "No Harmonic distortion",
        "Noise Free - Silent operation"
      ]
    }
  ];

  const specifications = [
    { parameter: "Ratings", "3Phase": "15 to 200 kVA", "1Phase": "3 Phase, 4 Wire" },
    { parameter: "Nature of Cooling", "3Phase": "Air Cooled", "1Phase": "Air Cooled" },
    { parameter: "Control Type", "3Phase": "True RMS Sensing and Correction", "1Phase": "20 kHz PWM switching using DSP" },
    { parameter: "Input Voltage Range", "3Phase": "360 V - 460 V @ 410 V", "1Phase": "320 V - 460 V @ 400 V" },
    { parameter: "Output Voltage", "3Phase": "410 V ±1%", "1Phase": "410 V ±1%" },
    { parameter: "Voltage Regulation", "3Phase": "±1%", "1Phase": "±1%" },
    { parameter: "Efficiency", "3Phase": ">98%", "1Phase": ">98%" },
    { parameter: "Input Frequency", "3Phase": "47 Hz to 53 Hz", "1Phase": "47 Hz to 53 Hz" },
    { parameter: "Wave Form", "3Phase": "Same as Input", "1Phase": "Same as Input" },
    { parameter: "Power Factor Effect", "3Phase": "Nil", "1Phase": "Nil" },
    { parameter: "Voltage Sensing Time", "3Phase": "< 10 ms", "1Phase": "< 10 ms" },
    { parameter: "Voltage Correction Time", "3Phase": "< 10 ms", "1Phase": "< 10 ms" },
    { parameter: "Under/Over Voltage Cutoff", "3Phase": "Electronic cutoff ±15%/10%", "1Phase": "Electronic cutoff ±15%/10%" },
    { parameter: "Overload Cutoff", "3Phase": "CT based @ 110%", "1Phase": "CT based @ 110%" },
    { parameter: "Short Circuit Protection", "3Phase": "MCB/MCCB provided", "1Phase": "MCB/MCCB provided" },
    { parameter: "Single Phasing Prevention", "3Phase": "Provided", "1Phase": "N/A" },
    { parameter: "Phase Reversal Trip", "3Phase": "Provided", "1Phase": "N/A" },
    { parameter: "Transient Protection", "3Phase": "Type 2 Surge Suppressor", "1Phase": "MOV at Output" },
    { parameter: "Manual Bypass", "3Phase": "Provided", "1Phase": "Provided" },
    { parameter: "Emergency Off-Switch", "3Phase": "Provided", "1Phase": "Provided" },
    { parameter: "Display Type", "3Phase": "7 Line LCD Panel", "1Phase": "7 Line LCD Panel" },
    { parameter: "Operating Temperature", "3Phase": "0 to 50°C", "1Phase": "0 to 50°C" },
    { parameter: "Duty Cycle", "3Phase": "Continuous", "1Phase": "Continuous" },
    { parameter: "IP Class", "3Phase": "IP-20, Indoor", "1Phase": "IP-20, Indoor" }
  ];

  return (
    <PageLayout
      title="Static Voltage Stabilizers"
      subtitle="Advanced power protection solutions for your needs"
      category="protect"
    >
      <div className="bg-white font-sans -mx-6 -mt-24 pt-16">
        {/* Hero Content Section - Compact Design */}
        <div className="w-full bg-black text-white relative">
          <div className="w-full px-4 md:px-8 py-12 md:py-16 relative z-10">
            <div className="max-w-5xl mx-auto text-center">
              <div className="space-y-6">
                <h2 className="text-3xl md:text-4xl font-bold leading-tight">
                  Static Voltage Stabilizers
                </h2>
                <p className="text-base md:text-lg text-gray-200 leading-relaxed max-w-3xl mx-auto">
                  Cutting-edge IGBT technology with DSP control delivering unparalleled voltage regulation and comprehensive power protection for industrial and commercial applications.
                </p>
                <div className="flex items-center justify-center gap-4">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                    ))}
                    <span className="ml-3 text-sm text-gray-200 font-medium">Industry Leading Technology</span>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row gap-4 justify-center pt-2">
                  <button
                    onClick={() => {
                      // Redirect to Sales page for quote
                      window.location.href = '/contact/sales';
                    }}
                    className="bg-white text-black px-6 py-3 rounded-lg font-semibold text-sm hover:bg-gray-100 transition-all duration-300 shadow-lg"
                  >
                    <Mail className="w-4 h-4 inline mr-2" />
                    Get Quote
                  </button>
                  <button
                    onClick={() => {
                      // Create a link to download the PDF brochure
                      const link = document.createElement('a');
                      link.href = '/brochures/static-stabilizers-brochure.pdf';
                      link.download = 'Static-Stabilizers-Brochure.pdf';
                      link.click();
                    }}
                    className="bg-gray-800 text-white px-6 py-3 rounded-lg font-semibold text-sm hover:bg-gray-700 transition-all duration-300 shadow-lg"
                  >
                    <Download className="w-4 h-4 inline mr-2" />
                    Download Brochure
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

      {/* Overview Section - Compact Design */}
      <div className="w-full bg-white">
        <div className="w-full px-4 md:px-8 py-12">
          <div className="text-center mb-10">
            <h2 className="text-2xl md:text-3xl font-bold text-black mb-4">
              Advanced Power Protection Solutions
            </h2>
            <p className="text-sm md:text-base text-black leading-relaxed max-w-4xl mx-auto">
              Our Static Voltage Stabilizers combine cutting-edge IGBT technology with DSP control to deliver unparalleled voltage regulation and comprehensive power protection for industrial and commercial applications. Built to meet international standards with superior performance and reliability.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-all duration-300 border border-gray-200">
                <div className="flex flex-col items-center text-center mb-4">
                  <div className="bg-black rounded-lg p-3 shadow-lg mb-3">
                    <div className="text-white">{feature.icon}</div>
                  </div>
                  <h3 className="text-lg font-bold text-black">{feature.title}</h3>
                </div>
                <ul className="space-y-3">
                  {feature.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start">
                      <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 mr-2 flex-shrink-0" />
                      <span className="text-sm text-black leading-relaxed font-medium">{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Technical Specifications Section - Mobile Optimized */}
      <div className="w-full bg-gray-50">
        <div className="w-full px-4 md:px-8 py-12">
          <div className="text-center mb-8">
            <h2 className="text-2xl md:text-3xl font-bold text-black mb-3">
              Technical Specifications
            </h2>
            <p className="text-sm md:text-base text-black leading-relaxed max-w-3xl mx-auto">
              Detailed technical parameters and standards ensuring optimal performance and reliability across all applications.
            </p>
          </div>

          <div className="specs-table-container">
            <div className="specs-table-scroll">
              <table className="specs-table">
                <thead>
                  <tr>
                    <th>Parameter</th>
                    <th>3-Phase</th>
                    <th>1-Phase / 2-Phase</th>
                  </tr>
                </thead>
                <tbody>
                  {specifications.map((spec, index) => (
                    <tr key={index}>
                      <td>{spec.parameter}</td>
                      <td>{spec['3Phase']}</td>
                      <td>{spec['1Phase']}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="bg-gray-100 px-4 py-3 border-t border-gray-200">
              <p className="text-xs text-black font-medium flex items-center justify-center">
                <Info className="w-4 h-4 inline mr-2 text-black" />
                All specifications are subject to standard tolerances and testing conditions as per international standards.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Key Features Section - Compact Design */}
      <div className="w-full bg-white">
        <div className="w-full px-4 md:px-8 py-12">
          <div className="text-center mb-10">
            <h2 className="text-2xl md:text-3xl font-bold text-black mb-4">
              Key Features & Benefits
            </h2>
            <p className="text-sm md:text-base text-black leading-relaxed max-w-4xl mx-auto">
              Discover the advanced features that make our Static Voltage Stabilizers the preferred choice for critical power applications. Each feature is designed to deliver maximum performance, safety, and reliability.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-all duration-300 border border-gray-200">
                <div className="flex items-center mb-6">
                  <div className="bg-black rounded-lg p-3 shadow-lg">
                    <div className="text-white">{feature.icon}</div>
                  </div>
                  <h3 className="text-lg font-bold text-black ml-4">{feature.title}</h3>
                </div>
                <div className="space-y-3">
                  {feature.items.map((item, itemIndex) => (
                    <div key={itemIndex} className="flex items-start">
                      <div className="bg-green-100 rounded-full p-1 mr-3 mt-0.5">
                        <CheckCircle className="w-4 h-4 text-green-600" />
                      </div>
                      <span className="text-sm text-black leading-relaxed font-medium">{item}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Call to Action Section - Compact Design */}
      <div className="w-full bg-black text-white">
        <div className="w-full px-4 md:px-8 py-12">
          <div className="text-center">
            <h2 className="text-2xl md:text-3xl font-bold mb-4">
              Ready to Upgrade Your Power Protection?
            </h2>
            <p className="text-sm md:text-base text-gray-200 mb-8 max-w-3xl mx-auto leading-relaxed">
              Get in touch with our experts to find the perfect Static Voltage Stabilizer solution for your needs. We provide comprehensive support from consultation to installation and maintenance.
            </p>
            <div className="flex justify-center">
              <button
                onClick={() => {
                  // Redirect to Sales page
                  window.location.href = '/contact/sales';
                }}
                className="bg-white text-black px-6 py-3 rounded-lg font-semibold text-sm hover:bg-gray-100 transition-all duration-300 shadow-lg"
              >
                <Mail className="w-4 h-4 inline mr-2" />
                Contact Expert
              </button>
            </div>
          </div>
        </div>
      </div>
      </div>
    </PageLayout>
  );
};

export default StaticStabilizersProduct;