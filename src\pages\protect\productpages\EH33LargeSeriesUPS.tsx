import React, { useState, useEffect, useRef } from 'react';
import { ChevronRight, Info, Check, ArrowUpRight, Award, Zap, Shield, Clock, BarChart3, ArrowRight, FileText } from 'lucide-react';
import PageLayout from "@/components/layout/PageLayout";
import { motion, AnimatePresence } from 'framer-motion';

const ProductSpecification = () => {
  // Enhanced mobile table styles - same design as EH11 but with black theme
  const customStyles = `
    .specs-table-container {
      position: relative;
      width: 100%;
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .specs-table-scroll {
      overflow-x: auto;
      overflow-y: visible;
      -webkit-overflow-scrolling: touch;
      scrollbar-width: thin;
      scrollbar-color: #000000 #f1f5f9;
    }

    .specs-table-scroll::-webkit-scrollbar {
      height: 12px;
    }

    .specs-table-scroll::-webkit-scrollbar-track {
      background: #f1f5f9;
      border-radius: 6px;
      margin: 0 4px;
    }

    .specs-table-scroll::-webkit-scrollbar-thumb {
      background: linear-gradient(90deg, #000000, #333333);
      border-radius: 6px;
      border: 2px solid #f1f5f9;
    }

    .specs-table-scroll::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(90deg, #333333, #555555);
    }

    .specs-table-scroll::-webkit-scrollbar-thumb:active {
      background: linear-gradient(90deg, #555555, #777777);
    }

    .specs-table {
      width: 100%;
      min-width: 1000px;
      border-collapse: separate;
      border-spacing: 0;
      background: white;
    }

    .specs-header-cell {
      background: linear-gradient(135deg, #000000 0%, #333333 100%);
      color: white;
      font-weight: bold;
      text-align: left;
      padding: 16px 12px;
      font-size: 14px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      position: sticky;
      top: 0;
      z-index: 20;
    }

    .specs-data-cell {
      padding: 12px;
      border-bottom: 1px solid #e5e7eb;
      font-size: 14px;
      font-weight: 500;
      color: #000000;
      background: white;
      transition: background-color 0.2s ease;
    }

    .specs-data-cell:hover {
      background: #f8fafc;
    }

    .specs-category-cell {
      background: #f8fafc;
      font-weight: 600;
      color: #000000;
      border-left: 4px solid #000000;
      padding: 12px 16px;
    }

    .sticky-header-mobile {
      position: sticky;
      left: 0;
      z-index: 30;
      background: linear-gradient(135deg, #000000 0%, #333333 100%);
    }

    .sticky-cell-mobile {
      position: sticky;
      left: 0;
      z-index: 10;
      border-right: 2px solid #e5e7eb;
    }

    @media (max-width: 768px) {
      .specs-table-scroll {
        padding-bottom: 8px;
      }

      .specs-table-scroll::-webkit-scrollbar {
        height: 14px;
      }

      .specs-table-scroll::-webkit-scrollbar-track {
        background: #e2e8f0;
        border-radius: 7px;
        margin: 0 8px;
      }

      .specs-table-scroll::-webkit-scrollbar-thumb {
        background: linear-gradient(90deg, #000000, #333333);
        border-radius: 7px;
        border: 3px solid #e2e8f0;
        min-width: 40px;
      }

      .specs-table {
        min-width: 800px;
        font-size: 13px;
      }

      .specs-header-cell {
        padding: 12px 8px;
        font-size: 11px;
      }

      .specs-data-cell {
        padding: 10px 8px;
        font-size: 12px;
      }

      .specs-category-cell {
        padding: 10px 12px;
        font-size: 12px;
      }
    }

    @media (max-width: 640px) {
      .specs-table-scroll {
        padding-bottom: 10px;
      }

      .specs-table-scroll::-webkit-scrollbar {
        height: 16px;
      }

      .specs-table-scroll::-webkit-scrollbar-track {
        background: #cbd5e1;
        border-radius: 8px;
        margin: 0 10px;
      }

      .specs-table-scroll::-webkit-scrollbar-thumb {
        background: linear-gradient(90deg, #000000, #333333);
        border-radius: 8px;
        border: 4px solid #cbd5e1;
        min-width: 50px;
      }

      .specs-table {
        min-width: 700px;
        font-size: 12px;
      }

      .specs-header-cell {
        padding: 10px 6px;
        font-size: 10px;
      }

      .specs-data-cell {
        padding: 8px 6px;
        font-size: 11px;
      }

      .specs-category-cell {
        padding: 8px 10px;
        font-size: 11px;
      }
    }

    /* Additional mobile scroll indicators and enhanced scrollbar */
    @media (max-width: 768px) {
      .specs-table-container {
        position: relative;
      }

      .specs-table-container::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 20px;
        height: 100%;
        background: linear-gradient(to left, rgba(255,255,255,0.9), transparent);
        pointer-events: none;
        z-index: 10;
      }

      .specs-table-container::before {
        content: '→';
        position: absolute;
        top: 50%;
        right: 8px;
        transform: translateY(-50%);
        color: #000000;
        font-size: 16px;
        font-weight: bold;
        z-index: 15;
        animation: scrollHint 2s ease-in-out infinite;
      }

      /* Force scrollbar visibility on mobile */
      .specs-table-scroll {
        scrollbar-width: auto !important;
        -webkit-overflow-scrolling: touch;
        overflow-x: scroll !important;
        padding-bottom: 20px;
      }

      .specs-table-scroll::-webkit-scrollbar {
        height: 18px !important;
        background: #f1f5f9;
        border-radius: 9px;
      }

      .specs-table-scroll::-webkit-scrollbar-thumb {
        background: linear-gradient(90deg, #000000, #333333) !important;
        border-radius: 9px;
        border: 3px solid #f1f5f9;
        min-width: 60px;
      }

      .specs-table-scroll::-webkit-scrollbar-track {
        background: #e2e8f0;
        border-radius: 9px;
        margin: 0 10px;
      }
    }

    @keyframes scrollHint {
      0%, 100% { opacity: 0.7; transform: translateY(-50%) translateX(0); }
      50% { opacity: 1; transform: translateY(-50%) translateX(3px); }
    }
  `;

  // Inject styles into document head
  useEffect(() => {
    const styleElement = document.createElement('style');
    styleElement.textContent = customStyles;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  const [activeTab, setActiveTab] = useState('features');
  const [isHeaderVisible, setIsHeaderVisible] = useState(false);
  const [showSpec, setShowSpec] = useState(false);
  const [hoveredModel, setHoveredModel] = useState(null);
  const [showPdfViewer, setShowPdfViewer] = useState(false);
  const [scrollPosition, setScrollPosition] = useState(0);
  const tabsRef = useRef(null);

  // Custom tab change handler with scroll position preservation
  const handleTabChange = (tabId) => {
    // Store current scroll position
    setScrollPosition(window.scrollY);
    // Update active tab
    setActiveTab(tabId);
  };

  // Restore scroll position after tab changes
  useEffect(() => {
    // Small delay to ensure DOM has updated
    const timer = setTimeout(() => {
      if (scrollPosition > 0) {
        window.scrollTo({
          top: scrollPosition,
          behavior: 'auto' // Use 'auto' instead of 'smooth' to prevent animation
        });
      }
    }, 10);

    return () => clearTimeout(timer);
  }, [activeTab, scrollPosition]);

  useEffect(() => {
    setIsHeaderVisible(true);

    // Show specification section after a delay
    const timer = setTimeout(() => {
      setShowSpec(true);
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  const tabs = [
    { id: 'features', label: 'Features' },
    { id: 'advantages', label: 'Advantages' },
    { id: 'benefits', label: 'Benefits' }
  ];

  const featuresList = [
    { title: 'Wide input voltage range (± 20%)', desc: 'Protects against unstable input and extends battery life' },
    { title: 'Compact footprint', desc: 'Smallest design for three phase UPS' },
    { title: 'Front access maintenance', desc: 'Easier installation and service' },
    { title: 'Frequency range (46 ~ 54 Hz)', desc: 'Immune to unstable sources' },
    { title: 'Dual feed capability', desc: 'Provides redundant configuration' },
    { title: 'Parallel capability', desc: 'Ideal for high-tier load applications' },
    { title: 'Online Double conversion with Advanced dual core DSP control', desc: 'Full Digital control for highest performance' },
    { title: 'Self-diagnostics', desc: 'Built-in electronic protection' },
    { title: 'Advanced battery management', desc: 'Automatic battery test including deep discharge protection' },
    { title: 'Extensive software & connectivity options', desc: 'Complete solution for advanced applications' },
    { title: 'Transformerless Design', desc: 'Maximizes the Power Factor to 0.9 or higher' },
    { title: 'Power Factor Correction (> 0.99)', desc: 'Up to PF 0.99' },
    { title: 'Low Harmonic Load', desc: '< 3% THDi' },
    { title: 'Zero Technical Load' }
  ];

  const advantagesList = [
    { title: 'Maintenance Bypass Switch (optional)', desc: 'Inbuilt Battery Cabinet' },
    { title: 'Current Generator Overload due to starting inrush currents', desc: 'Sensitive medical equipment' },
    { title: 'On-line double conversion & full Digital Frequency Converter', desc: 'Lower total harmonic distortion (< 3%)' },
    { title: 'Built-in system protection diagnostic', desc: 'SNMP / USB Option compatibility' },
    { title: 'Advance backfeed protection circuit design', desc: 'Various operating modes' },
    { title: 'Power operation function', desc: 'Higher efficiency (up to 98% in ECO mode)' },
    { title: 'Including voltage sensor under voltage load', desc: 'Overvoltage protection (optional)' },
    { title: 'Automatic bypass', desc: 'Bypass for fault clearing' },
    { title: 'Built-in DC fuses', desc: 'Advance Battery monitoring in graphic display' },
    { title: 'Power protection for redundancy & load stability', desc: '0% to 100% step load change without transfer to Bypass' },
    { title: 'ECO mode operation', desc: 'AC-Power Quality Line Bridge' },
    { title: 'Programmable alarm for warning', desc: 'Better protection through advance FW Control Quality Line Bridge' },
    { title: 'Low Operating Cost', desc: 'High Efficiency - Upto 95% or better' },
    { title: 'Reduction in carbon footprint', desc: 'Smaller size than legacy systems' },
    { title: 'Increase in IT floor space', desc: 'Lower cost of AC Power' },
    { title: 'Better efficiency related to lower heat, saving up to 40%', desc: 'No transformer required' },
    { title: 'Reduction floor space' }
  ];

  const benefitsList = [
    {
      title: 'High Uptime / Availability',
      desc: 'Ensures your critical systems remain operational with minimal to zero interruption, delivering maximum operational continuity.'
    },
    {
      title: 'High Flexibility',
      desc: 'Adapts to various configurations and settings, supporting a wide range of devices and operational environments.'
    },
    {
      title: 'High Reliability',
      desc: 'Engineered with premium components and advanced protection systems to ensure consistent and dependable performance.'
    },
    {
      title: 'Low Total Cost of Ownership (TCO)',
      desc: 'Combines energy efficiency, extended lifespan, and reduced maintenance requirements to minimize long-term costs.'
    }
  ];

  const specifications = [
    { category: 'MODEL', model1: 'EH - 80', model2: 'EH - 100', model3: 'EH - 120', model4: 'EH - 160', model5: 'EH - 200' },
    { category: 'Rated Capacity', model1: '80 kVA / 80 kW', model2: '100 kVA / 100 kW', model3: '120 kVA / 120 kW', model4: '160 kVA / 160 kW', model5: '200 kVA / 200 kW' },
    { category: 'INPUT', model1: '', model2: '', model3: '', model4: '', model5: '' },
    { category: 'Phase', model1: 'Three phase five-wire (3Ph + N + PE)', model2: 'Three phase five-wire (3Ph + N + PE)', model3: 'Three phase five-wire (3Ph + N + PE)', model4: 'Three phase five-wire (3Ph + N + PE)', model5: 'Three phase five-wire (3Ph + N + PE)' },
    { category: 'Rated Voltage', model1: '380 / 400 / 415 VAC', model2: '380 / 400 / 415 VAC', model3: '380 / 400 / 415 VAC', model4: '380 / 400 / 415 VAC', model5: '380 / 400 / 415 VAC' },
    { category: 'Voltage Range', model1: '304 ~ 478 VAC, Load = 80% 228 ~ 304 VAC, Load decreases linearly with min phase voltage', model2: '304 ~ 478 VAC, Load = 80% 228 ~ 304 VAC, Load decreases linearly with min phase voltage', model3: '304 ~ 478 VAC, Load = 80% 228 ~ 304 VAC, Load decreases linearly with min phase voltage', model4: '304 ~ 478 VAC, Load = 80% 228 ~ 304 VAC, Load decreases linearly with min phase voltage', model5: '304 ~ 478 VAC, Load = 80% 228 ~ 304 VAC, Load decreases linearly with min phase voltage' },
    { category: 'Rated Frequency', model1: '50 / 60 Hz (auto-sensing)', model2: '50 / 60 Hz (auto-sensing)', model3: '50 / 60 Hz (auto-sensing)', model4: '50 / 60 Hz (auto-sensing)', model5: '50 / 60 Hz (auto-sensing)' },
    { category: 'Frequency Range', model1: '46 ~ 54 Hz', model2: '46 ~ 54 Hz', model3: '46 ~ 54 Hz', model4: '46 ~ 54 Hz', model5: '46 ~ 54 Hz' },
    { category: 'Power Factor', model1: '≥ 0.99', model2: '≥ 0.99', model3: '≥ 0.99', model4: '≥ 0.99', model5: '≥ 0.99' },
    { category: 'Bypass Voltage Range', model1: 'Settable, default, +10%/-10%', model2: 'Settable, default, +10%/-10%', model3: 'Settable, default, +10%/-10%', model4: 'Settable, default, +10%/-10%', model5: 'Settable, default, +10%/-10%' },
    { category: 'Current Harmonic Distortion (THDi)', model1: 'Up to linear: < 3%, +15%, +20% or +25%; Low: -10%, -15%, -20%, -30%, -40%', model2: 'Up to linear: < 3%, +15%, +20% or +25%; Low: -10%, -15%, -20%, -30%, -40%', model3: 'Up to linear: < 3%, +15%, +20% or +25%; Low: -10%, -15%, -20%, -30%, -40%', model4: 'Up to linear: < 3%, +15%, +20% or +25%; Low: -10%, -15%, -20%, -30%, -40%', model5: 'Up to linear: < 3%, +15%, +20% or +25%; Low: -10%, -15%, -20%, -30%, -40%' },
    { category: 'OUTPUT', model1: '', model2: '', model3: '', model4: '', model5: '' },
    { category: 'Output Wiring', model1: 'Three phase five-wire (3Ph + N + PE)', model2: 'Three phase five-wire (3Ph + N + PE)', model3: 'Three phase five-wire (3Ph + N + PE)', model4: 'Three phase five-wire (3Ph + N + PE)', model5: 'Three phase five-wire (3Ph + N + PE)' },
    { category: 'Rated Voltage', model1: '380/400/415 VAC', model2: '380/400/415 VAC', model3: '380/400/415 VAC', model4: '380/400/415 VAC', model5: '380/400/415 VAC' },
    { category: 'Voltage Regulation', model1: '±1% (linear load)', model2: '±1% (linear load)', model3: '±1% (linear load)', model4: '±1% (linear load)', model5: '±1% (linear load)' },
    { category: 'Frequency', model1: 'Synchronized with utility in mains mode; 50/60 Hz ±0.1% in battery mode', model2: 'Synchronized with utility in mains mode; 50/60 Hz ±0.1% in battery mode', model3: 'Synchronized with utility in mains mode; 50/60 Hz ±0.1% in battery mode', model4: 'Synchronized with utility in mains mode; 50/60 Hz ±0.1% in battery mode', model5: 'Synchronized with utility in mains mode; 50/60 Hz ±0.1% in battery mode' },
    { category: 'Power Factor', model1: '1', model2: '1', model3: '1', model4: '1', model5: '1' },
    { category: 'Total Harmonic Distortion (THDv)', model1: '< 1% (linear load), < 3% (non-linear load)', model2: '< 1% (linear load), < 3% (non-linear load)', model3: '< 1% (linear load), < 3% (non-linear load)', model4: '< 1% (linear load), < 3% (non-linear load)', model5: '< 1% (linear load), < 3% (non-linear load)' },
    { category: 'Crest Factor', model1: '3:1', model2: '3:1', model3: '3:1', model4: '3:1', model5: '3:1' },
    { category: 'Overload', model1: '110% for 60 min, 125% for 10 min, 150% for 1 min, >150% for 200ms', model2: '110% for 60 min, 125% for 10 min, 150% for 1 min, >150% for 200ms', model3: '110% for 60 min, 125% for 10 min, 150% for 1 min, >150% for 200ms', model4: '110% for 60 min, 125% for 10 min, 150% for 1 min, >150% for 200ms', model5: '110% for 60 min, 125% for 10 min, 150% for 1 min, >150% for 200ms' },
    { category: 'Efficiency', model1: '≥ 95%', model2: '≥ 95%', model3: '≥ 95%', model4: '≥ 95%', model5: '≥ 95%' },
    { category: 'DC VOLTAGE', model1: '±240 VDC', model2: '±240 VDC', model3: '±240 VDC', model4: '±240 VDC', model5: '±240 VDC' },
    { category: 'BATTERY', model1: '', model2: '', model3: '', model4: '', model5: '' },
    { category: 'No. of Batteries', model1: '40 (20+20) 12V', model2: '40 (20+20) 12V', model3: '40 (20+20) 12V', model4: '40 (20+20) 12V', model5: '40 (20+20) 12V' },
    { category: 'Charging Current (max)', model1: '20 A', model2: '20 A', model3: '40 A', model4: '40 A', model5: '80 A' },
    { category: 'Backup Time', model1: 'Depends on capacity of battery', model2: 'Depends on capacity of battery', model3: 'Depends on capacity of battery', model4: 'Depends on capacity of battery', model5: 'Depends on capacity of battery' },
    { category: 'DISPLAY', model1: 'Operational data: Load level, Load percentage, Battery level, Input voltage, Input frequency, Output voltage, Output frequency, Ambient temperature', model2: 'Operational data: Load level, Load percentage, Battery level, Input voltage, Input frequency, Output voltage, Output frequency, Ambient temperature', model3: 'Operational data: Load level, Load percentage, Battery level, Input voltage, Input frequency, Output voltage, Output frequency, Ambient temperature', model4: 'Operational data: Load level, Load percentage, Battery level, Input voltage, Input frequency, Output voltage, Output frequency, Ambient temperature', model5: 'Operational data: Load level, Load percentage, Battery level, Input voltage, Input frequency, Output voltage, Output frequency, Ambient temperature' },
    { category: 'Protection', model1: 'Short-circuit, Over load, Over temperature, Battery low voltage, Over voltage, Under voltage, Fan failure', model2: 'Short-circuit, Over load, Over temperature, Battery low voltage, Over voltage, Under voltage, Fan failure', model3: 'Short-circuit, Over load, Over temperature, Battery low voltage, Over voltage, Under voltage, Fan failure', model4: 'Short-circuit, Over load, Over temperature, Battery low voltage, Over voltage, Under voltage, Fan failure', model5: 'Short-circuit, Over load, Over temperature, Battery low voltage, Over voltage, Under voltage, Fan failure' },
    { category: 'Max. no. of parallel connections', model1: '6', model2: '6', model3: '6', model4: '6', model5: '6' },
    { category: 'Communications', model1: 'Standard configuration: RS232, USB, EPO, Dry relay contact; Optional configuration: SNMP card, AS400, Modbus card, USB/RS485 card', model2: 'Standard configuration: RS232, USB, EPO, Dry relay contact; Optional configuration: SNMP card, AS400, Modbus card, USB/RS485 card', model3: 'Standard configuration: RS232, USB, EPO, Dry relay contact; Optional configuration: SNMP card, AS400, Modbus card, USB/RS485 card', model4: 'Standard configuration: RS232, USB, EPO, Dry relay contact; Optional configuration: SNMP card, AS400, Modbus card, USB/RS485 card', model5: 'Standard configuration: RS232, USB, EPO, Dry relay contact; Optional configuration: SNMP card, AS400, Modbus card, USB/RS485 card' },
    { category: 'Alarms', model1: 'LED / LCD', model2: 'LED / LCD', model3: 'LED / LCD', model4: 'LED / LCD', model5: 'LED / LCD' },
    { category: 'Noise Level (at 1 meter)', model1: '< 65 dB', model2: '< 68 dB', model3: '< 70 dB', model4: '< 72 dB', model5: '< 73 dB' },
    { category: 'Operating Temperature', model1: '0°C ~ 40°C', model2: '0°C ~ 40°C', model3: '0°C ~ 40°C', model4: '0°C ~ 40°C', model5: '0°C ~ 40°C' },
    { category: 'Storage Temperature', model1: '-25°C ~ 55°C', model2: '-25°C ~ 55°C', model3: '-25°C ~ 55°C', model4: '-25°C ~ 55°C', model5: '-25°C ~ 55°C' },
    { category: 'Relative Humidity', model1: '0% ~ 95% (non-condensing)', model2: '0% ~ 95% (non-condensing)', model3: '0% ~ 95% (non-condensing)', model4: '0% ~ 95% (non-condensing)', model5: '0% ~ 95% (non-condensing)' },
    { category: 'Altitude', model1: '< 1000 m, derate 1% per 100 m between 1000 m-2000 m', model2: '< 1000 m, derate 1% per 100 m between 1000 m-2000 m', model3: '< 1000 m, derate 1% per 100 m between 1000 m-2000 m', model4: '< 1000 m, derate 1% per 100 m between 1000 m-2000 m', model5: '< 1000 m, derate 1% per 100 m between 1000 m-2000 m' },
    { category: 'PHYSICAL', model1: '', model2: '', model3: '', model4: '', model5: '' },
    { category: 'Tower Model (W × D × H)', model1: '600 × 900 × 1600', model2: '600 × 900 × 1600', model3: '600 × 900 × 1600', model4: '600 × 1000 × 1600', model5: '600 × 1000 × 2000' },
    { category: 'Net Weight (kg)', model1: '182', model2: '196', model3: '196', model4: '258', model5: '306' }
  ];

  const renderContent = () => {
    switch(activeTab) {
      case 'features':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
            {featuresList.map((feature, index) => (
              <motion.div
                key={index}
                className="bg-white p-4 md:p-6 rounded-xl border border-gray-200 shadow-md hover:shadow-xl transition-all duration-300 relative overflow-hidden"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                whileHover={{
                  y: -5,
                  boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
                }}
              >
                {/* Decorative gradient accent */}
                <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-black to-gray-800"></div>

                <div className="flex items-start gap-3 md:gap-4">
                  <div className="mt-1 text-black bg-gray-100 p-2 rounded-full flex-shrink-0">
                    <Check size={16} className="text-black" />
                  </div>
                  <div>
                    <h4 className="font-bold text-base md:text-lg text-black mb-1 md:mb-2 break-words">{feature.title}</h4>
                    {feature.desc && <p className="text-black text-sm md:text-base">{feature.desc}</p>}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        );
      case 'advantages':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
            {advantagesList.map((advantage, index) => (
              <motion.div
                key={index}
                className="bg-white p-4 md:p-6 rounded-xl border border-gray-200 shadow-md hover:shadow-xl transition-all duration-300 relative overflow-hidden"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                whileHover={{
                  y: -5,
                  boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
                }}
              >
                {/* Decorative gradient accent */}
                <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-gray-800 to-black"></div>

                <div className="flex items-start gap-3 md:gap-4">
                  <div className="mt-1 text-black bg-gray-100 p-2 rounded-full flex-shrink-0">
                    <ArrowUpRight size={16} className="text-black" />
                  </div>
                  <div>
                    <h4 className="font-bold text-base md:text-lg text-black mb-1 md:mb-2 break-words">{advantage.title}</h4>
                    {advantage.desc && <p className="text-black text-sm md:text-base">{advantage.desc}</p>}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        );
      case 'benefits':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
            {benefitsList.map((benefit, index) => (
              <motion.div
                key={index}
                className="bg-white p-5 md:p-8 rounded-xl border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{
                  y: -5,
                  boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
                }}
              >
                {/* Background decorative elements - hidden on small screens */}
                <div className="absolute top-0 right-0 w-24 md:w-32 h-24 md:h-32 bg-gray-100 opacity-40 rounded-full transform translate-x-12 md:translate-x-16 -translate-y-12 md:-translate-y-16 z-0"></div>

                <div className="relative z-10">
                  <div className="flex flex-col md:flex-row md:items-center gap-3 md:gap-4 mb-3 md:mb-4">
                    <div className="text-black bg-gray-100 p-2 md:p-3 rounded-full w-10 h-10 md:w-auto md:h-auto flex items-center justify-center">
                      <Award size={20} className="text-black" />
                    </div>
                    <h3 className="font-bold text-xl md:text-2xl text-black">{benefit.title}</h3>
                  </div>

                  <p className="text-black text-sm md:text-base md:pl-16">{benefit.desc}</p>
                </div>
              </motion.div>
            ))}
          </div>
        );
      default:
        return null;
    }
  };

  // Stats for Key Features Section
  const keyStats = [
    { value: "98", suffix: "%", title: "Max Efficiency", icon: <Zap size={24} /> },
    { value: "0", suffix: "ms", title: "Transfer Time", icon: <Clock size={24} /> },
    { value: "1.0", suffix: "", title: "Power Factor", icon: <Shield size={24} /> },
    { value: "3", suffix: "Ph", title: "Three Phase", icon: <BarChart3 size={24} /> }
  ];

  // PDF Viewer Modal with improved mobile compatibility
  const PdfViewer = () => {
    const pdfUrl = "/Krykard Online UPS January 2025. (1).pdf";
    const [isMobile, setIsMobile] = useState(false);

    // Check if device is mobile
    useEffect(() => {
      const checkMobile = () => {
        setIsMobile(window.innerWidth < 768);
      };

      // Initial check
      checkMobile();

      // Add resize listener
      window.addEventListener('resize', checkMobile);

      // Cleanup
      return () => window.removeEventListener('resize', checkMobile);
    }, []);

    // Function to directly download the PDF without any dialog
    const handleDownloadPdf = () => {
      window.open(pdfUrl, '_blank');
    };

    return (
      <div className={`fixed inset-0 z-50 flex items-center justify-center ${showPdfViewer ? '' : 'hidden'}`}>
        <div className="absolute inset-0 bg-black bg-opacity-80" onClick={() => setShowPdfViewer(false)}></div>
        <div className="relative bg-white rounded-xl p-4 md:p-6 w-[95%] md:w-full max-w-5xl max-h-[90vh] overflow-hidden shadow-2xl">
          {/* Close button - larger touch target for mobile */}
          <motion.button
            className="absolute top-3 right-3 text-gray-500 hover:text-gray-800 bg-white rounded-full p-2 shadow-md z-20"
            onClick={() => setShowPdfViewer(false)}
            whileHover={{ scale: 1.1, backgroundColor: "#f0f9ff" }}
            whileTap={{ scale: 0.95 }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </motion.button>

          <div className="flex flex-col md:flex-row md:items-center justify-between mb-4 pb-4 border-b">
            <h3 className="text-lg md:text-xl font-bold text-black pr-8 mb-3 md:mb-0">KSX4080 EH 33 Series Brochure</h3>
            <motion.button
              onClick={handleDownloadPdf}
              className="flex items-center justify-center gap-2 bg-black hover:bg-gray-800 text-white py-2 px-4 rounded-lg transition-colors shadow-md w-full md:w-auto"
              whileHover={{ scale: 1.05, boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)" }}
              whileTap={{ scale: 0.95 }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
              </svg>
              Download PDF
            </motion.button>
          </div>

          {/* Mobile-optimized PDF viewer */}
          <div className="w-full h-[50vh] md:h-[70vh] rounded-lg overflow-hidden shadow-inner">
            {isMobile ? (
              // Mobile-friendly alternative - direct download prompt
              <div className="flex flex-col items-center justify-center h-full bg-gray-50 rounded-lg p-6 text-center">
                <div className="bg-gray-200 p-4 rounded-full mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-black" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h4 className="text-lg font-bold text-black mb-2">PDF Viewer</h4>
                <p className="text-black mb-6">
                  For the best experience on mobile devices, we recommend downloading the PDF brochure.
                </p>
                <motion.button
                  onClick={handleDownloadPdf}
                  className="flex items-center gap-2 bg-black hover:bg-gray-800 text-white py-3 px-6 rounded-lg transition-colors shadow-md w-full justify-center"
                  whileHover={{ scale: 1.05, boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)" }}
                  whileTap={{ scale: 0.95 }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                  Download PDF
                </motion.button>
              </div>
            ) : (
              // Desktop PDF viewer
              <object
                data={pdfUrl}
                type="application/pdf"
                className="w-full h-full"
              >
                <div className="flex flex-col items-center justify-center h-full bg-gray-100 rounded-lg p-8 text-center">
                  <p className="text-gray-600 mb-4">
                    PDF preview is not available in your browser.
                  </p>
                  <motion.button
                    onClick={handleDownloadPdf}
                    className="flex items-center gap-2 bg-black hover:bg-gray-800 text-white py-3 px-6 rounded-lg transition-colors shadow-md"
                    whileHover={{ scale: 1.05, boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)" }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    Download PDF
                  </motion.button>
                </div>
              </object>
            )}
          </div>
        </div>
      </div>
    );
  };

  const ProductSpecContent = () => (
    <div className="w-full mx-auto font-sans">
      {/* Compact Hero Section */}
      <section className="py-8 md:py-12 relative overflow-hidden">
        <div className="relative z-10 px-4 max-w-7xl mx-auto">
          <motion.div
            className="text-black p-3 md:p-6 overflow-hidden relative mb-6 md:mb-10"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
          >
            <div className="relative z-10">
              <motion.h1
                className="text-2xl md:text-3xl lg:text-4xl font-extrabold tracking-tight mb-3 md:mb-4 text-black break-words"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.2 }}
              >
                KRYKARD EH 33 SERIES <span className="text-black">3/3 UPS</span>
              </motion.h1>

              <motion.p
                className="text-sm md:text-base font-medium mb-4 md:mb-6 text-black"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.3 }}
              >
                80 kVA to 200 kVA - The high-frequency transformer-less static converter UPS
              </motion.p>

              <motion.div
                className="bg-gradient-to-r from-black to-gray-800 text-white font-bold py-2 md:py-3 px-3 md:px-6 rounded-lg inline-block shadow-lg transform hover:scale-105 transition-transform duration-300 text-xs md:text-sm text-center"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.7, delay: 0.4 }}
              >
                HIGH PERFORMANCE POWER PROTECTION FOR ENTERPRISE INFRASTRUCTURE
              </motion.div>
            </div>
          </motion.div>

          {/* Compact Hero Content Area */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-10 items-center mb-10 md:mb-12">
            {/* Left side: Content */}
            <motion.div
              className="space-y-4 md:space-y-6"
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <h2 className="text-xl md:text-2xl font-bold text-black mb-2 md:mb-3">Enterprise-Grade Three Phase Power Protection</h2>
                <div className="h-1 w-20 bg-black rounded-full mb-3 md:mb-4"></div>
                <p className="text-sm md:text-base text-black leading-relaxed">
                  The KRYKARD EH 33 Series UPS delivers robust, reliable three-phase power protection for mission-critical data centers and industrial applications with high-efficiency operation and advanced power management features.
                </p>
              </motion.div>

              <motion.div
                className="mt-3 md:mt-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <h3 className="text-base md:text-lg font-bold mb-2 md:mb-3 text-black">Perfect for:</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 md:gap-3">
                  {[
                    {icon: "🏢", text: "Enterprise Data Centers"},
                    {icon: "🏥", text: "Large Healthcare Facilities"},
                    {icon: "🏭", text: "Industrial Manufacturing"},
                    {icon: "💻", text: "Network Operations Centers"},
                    {icon: "🔌", text: "Telecom Infrastructure"}
                  ].map((item, index) => (
                    <motion.div
                      key={index}
                      className="flex items-center group"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: 0.4 + index * 0.1 }}
                    >
                      <motion.div
                        className="flex items-center justify-center w-6 h-6 md:w-8 md:h-8 bg-gray-100 rounded-full mr-2 md:mr-3 transform group-hover:scale-110 transition-transform flex-shrink-0"
                        animate={{
                          y: [0, -3, 0],
                          rotate: [-1, 1, -1]
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          repeatType: "reverse"
                        }}
                      >
                        <span className="text-sm md:text-base">{item.icon}</span>
                      </motion.div>
                      <span className="text-black font-medium group-hover:text-gray-700 transition-colors text-xs md:text-sm">
                        {item.text}
                      </span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              <motion.div
                className="flex flex-col sm:flex-row gap-2 md:gap-3"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <motion.a
                  href="/contact/sales"
                  className="bg-gradient-to-r from-black to-gray-800 hover:from-gray-800 hover:to-black text-white px-3 md:px-4 py-2 md:py-3 rounded-lg shadow-lg flex items-center justify-center gap-2 transition-all duration-300 sm:w-1/2 text-xs md:text-sm"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <span>Request Quote</span>
                  <ArrowRight size={14} />
                </motion.a>

                <motion.a
                  href="/Krykard Online UPS January 2025. (1).pdf"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="border-2 border-black text-black hover:bg-gray-50 px-3 md:px-4 py-2 md:py-3 rounded-lg shadow-lg transition-all duration-300 flex items-center justify-center gap-2 sm:w-1/2 text-xs md:text-sm"
                  whileHover={{
                    scale: 1.02,
                    boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.4)"
                  }}
                  whileTap={{ scale: 0.98 }}
                >
                  <FileText size={14} />
                  <span>View Brochure</span>
                </motion.a>
              </motion.div>
            </motion.div>            {/* Right side: UPS Image */}
            <motion.div
              className="relative flex justify-center"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
            >
              <div className="w-full max-w-2xl h-auto min-h-[300px] md:min-h-[400px] lg:h-[600px] flex items-center justify-center py-6">
                {/* Replace with actual image */}
                <motion.img
                  src="/UPS/2__1_-removebg-preview.png"
                  alt="EH 33 Series UPS"
                  className="max-w-full max-h-full object-contain"
                  whileHover={{ scale: 1.1 }}
                  animate={{
                    y: [0, -35, 0],
                  }}
                  transition={{
                    duration: 2.5,
                    repeat: Infinity,
                    repeatType: "reverse",
                    ease: "easeInOut"
                  }}
                />
              </div>
            </motion.div>
          </div>

          {/* Key Features Section */}
          <div className="mb-16">
            <motion.div
              className="text-center mb-10"
              initial={{ opacity: 0, y: -20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl font-bold text-black mb-3">Key Features</h2>
              <div className="h-1.5 w-24 bg-black mx-auto rounded-full"></div>
              <p className="mt-4 text-lg text-black max-w-2xl mx-auto">
                Core capabilities that define our enterprise UPS solutions
              </p>
            </motion.div>

            {/* Modern 3D Card Layout - Improved for mobile */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-8">
              {keyStats.map((stat, index) => {
                // Define different gradient colors for each card
                const gradients = [
                  "from-blue-500 to-blue-600",
                  "from-indigo-500 to-blue-500",
                  "from-blue-600 to-indigo-600",
                  "from-blue-600 to-blue-700"
                ];

                // Define different glow effects for each card
                const glows = [
                  "from-blue-400/20 via-blue-500/10 to-transparent",
                  "from-indigo-400/20 via-blue-500/10 to-transparent",
                  "from-blue-500/20 via-indigo-500/10 to-transparent",
                  "from-blue-600/20 via-blue-700/10 to-transparent"
                ];

                return (
                  <motion.div
                    key={index}
                    className="relative bg-white rounded-xl shadow-xl overflow-hidden border border-blue-100"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    whileHover={{
                      y: -10,
                      boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
                      transition: { duration: 0.2 }
                    }}
                  >
                    {/* Top gradient bar */}
                    <div className={`h-2 w-full bg-gradient-to-r ${gradients[index]}`}></div>

                    {/* Background glow effect */}
                    <div className={`absolute inset-0 bg-gradient-radial ${glows[index]} opacity-60`}></div>

                    <div className="p-4 md:p-8 relative z-10">
                      <div className="flex flex-col items-center text-center">
                        <div className={`w-12 h-12 md:w-16 md:h-16 rounded-full bg-gradient-to-br ${gradients[index]} flex items-center justify-center mb-3 md:mb-4 shadow-lg`}>
                          {React.cloneElement(stat.icon, { className: "text-white", size: 20 })}
                        </div>

                        <div className="mb-1 md:mb-2 flex items-baseline justify-center">
                          <span className="text-2xl md:text-4xl font-extrabold text-black">{stat.value}</span>
                          <span className="text-lg md:text-xl font-bold text-black ml-1">{stat.suffix}</span>
                        </div>

                        <h3 className="text-sm md:text-xl font-bold text-black">{stat.title}</h3>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </div>
      </section>

      {/* Modern Tabs Section with Enhanced Design */}
      <section className="max-w-7xl mx-auto px-4 mb-20 relative" ref={tabsRef}>
        <motion.div
          className="text-center mb-10"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold text-black mb-3 inline-block relative">
              Product Information
              <motion.div
                className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-blue-400 via-blue-600 to-blue-400"
                initial={{ scaleX: 0, opacity: 0 }}
                whileInView={{ scaleX: 1, opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
              />
            </h2>
          </motion.div>
        </motion.div>

        {/* Decorative background elements */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          <motion.div
            className="absolute top-20 right-0 w-96 h-96 bg-blue-50 rounded-full opacity-30 blur-3xl"
            animate={{
              scale: [1, 1.2, 1],
              x: [0, 20, 0],
              opacity: [0.3, 0.4, 0.3]
            }}
            transition={{
              duration: 15,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
          <motion.div
            className="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-tr from-indigo-50 to-blue-100/50 rounded-full opacity-50 blur-3xl"
            animate={{
              scale: [1, 1.3, 1],
              x: [-20, -30, -20],
              y: [20, 30, 20],
              opacity: [0.3, 0.6, 0.3]
            }}
            transition={{
              duration: 18,
              repeat: Infinity,
              repeatType: "reverse",
              delay: 2
            }}
          />
        </div>

        <motion.div
          className="relative z-10 bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-blue-100/50"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          {/* Mobile-Responsive Tab Navigation */}
          <div className="flex flex-col sm:flex-row justify-center mb-4 md:mb-6 bg-gray-50/50 p-2 rounded-xl overflow-hidden max-w-md sm:max-w-none mx-auto">
            {tabs.map((tab, index) => (
              <motion.button
                key={tab.id}
                className={`relative py-2 md:py-3 px-3 md:px-6 font-medium text-xs md:text-sm lg:text-base transition-all duration-300 rounded-xl z-10 mb-1 sm:mb-0 sm:m-1 flex-shrink-0 ${
                  activeTab === tab.id
                    ? 'bg-gradient-to-r from-black to-gray-800 text-white shadow-lg'
                    : 'text-black hover:text-gray-700 hover:bg-white/50'
                }`}
                onClick={() => {
                  // Store current scroll position
                  setScrollPosition(window.scrollY);
                  // Update active tab
                  setActiveTab(tab.id);
                }}
                whileHover={{
                  scale: 1.02,
                  boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.4)"
                }}
                whileTap={{ scale: 0.98 }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{
                  duration: 0.4,
                  delay: index * 0.1,
                  type: "spring",
                  stiffness: 200
                }}
              >
                {/* Icon indicators for each tab */}
                <div className="flex items-center gap-1 md:gap-2">
                  {tab.id === 'features' && (
                    <motion.div
                      animate={{ rotate: [0, 10, 0] }}
                      transition={{ duration: 2, repeat: Infinity, repeatType: "reverse" }}
                    >
                      <Check size={14} className={activeTab === tab.id ? "text-white" : "text-black"} />
                    </motion.div>
                  )}
                  {tab.id === 'advantages' && (
                    <motion.div
                      animate={{ scale: [1, 1.1, 1] }}
                      transition={{ duration: 2, repeat: Infinity, repeatType: "reverse" }}
                    >
                      <ArrowUpRight size={14} className={activeTab === tab.id ? "text-white" : "text-black"} />
                    </motion.div>
                  )}
                  {tab.id === 'benefits' && (
                    <motion.div
                      animate={{ y: [0, -3, 0] }}
                      transition={{ duration: 2, repeat: Infinity, repeatType: "reverse" }}
                    >
                      <Award size={14} className={activeTab === tab.id ? "text-white" : "text-black"} />
                    </motion.div>
                  )}
                  <span>{tab.label}</span>
                </div>

                {/* Active indicator dot */}
                {activeTab === tab.id && (
                  <motion.div
                    className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1.5 h-1.5 bg-white rounded-full"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    layoutId="activeTabIndicator"
                  />
                )}
              </motion.button>
            ))}
          </div>

          {/* Content with page transition */}
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{
                duration: 0.5,
                type: "spring",
                stiffness: 100
              }}
              className="relative z-10"
            >
              {renderContent()}
            </motion.div>
          </AnimatePresence>
        </motion.div>
      </section>

      {/* Compact Specifications Table Section */}
      <section className="w-full mb-12 md:mb-16 px-4 md:px-8 lg:px-12 overflow-hidden">
        <motion.div
          className="text-center mb-6 md:mb-8"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <h2 className="text-2xl md:text-3xl font-bold text-black mb-2 md:mb-3">Technical Specifications</h2>
          <div className="h-1 w-24 bg-black mx-auto rounded-full"></div>
          <p className="mt-3 md:mt-4 text-sm md:text-base text-black max-w-2xl mx-auto font-medium">
            Comprehensive technical details for the EH 33 Series UPS line
          </p>
        </motion.div>

        {/* EH11 Mobile Table Design - Same Structure */}
        <motion.div
          className="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-200 relative z-10"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          viewport={{ once: true }}
          whileHover={{ boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.1)" }}
        >
          {/* Specifications Table - Same Design as EH11 with Black Theme */}
          <div className="w-full">
            <div className="text-sm text-black mb-4 p-4 md:hidden bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg text-center font-medium border border-gray-200">
              <span>📱 Swipe horizontally to view all model specifications →</span>
            </div>
            <div className="specs-table-container">
              <div className="specs-table-scroll overflow-x-auto scrollbar-thin scrollbar-thumb-black scrollbar-track-gray-100">
                <table className="specs-table">
                <thead>
                    <tr className="bg-gradient-to-r from-black to-gray-800">
                      <th className="sticky-header-mobile py-3 px-3 text-left" style={{ minWidth: '140px' }}>
                        <div className="font-bold text-white text-xs">SPECIFICATIONS</div>
                      </th>
                      {specifications[0].model1 && (
                        <th className={`py-3 px-2 text-center ${hoveredModel === 'model1' || hoveredModel === null ? 'opacity-100' : 'opacity-70'} transition-opacity duration-300`} style={{ minWidth: '90px' }}>
                          <div className="font-bold text-white text-xs relative">
                            {specifications[0].model1}
                            {(hoveredModel === 'model1' || hoveredModel === null) && (
                              <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-white" />
                            )}
                          </div>
                        </th>
                      )}
                      {specifications[0].model2 && (
                        <th className={`py-3 px-2 text-center ${hoveredModel === 'model2' || hoveredModel === null ? 'opacity-100' : 'opacity-70'} transition-opacity duration-300`} style={{ minWidth: '90px' }}>
                          <div className="font-bold text-white text-xs relative">
                            {specifications[0].model2}
                            {(hoveredModel === 'model2' || hoveredModel === null) && (
                              <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-white" />
                            )}
                          </div>
                        </th>
                      )}
                      {specifications[0].model3 && (
                        <th className={`py-3 px-2 text-center ${hoveredModel === 'model3' || hoveredModel === null ? 'opacity-100' : 'opacity-70'} transition-opacity duration-300`} style={{ minWidth: '90px' }}>
                          <div className="font-bold text-white text-xs relative">
                            {specifications[0].model3}
                            {(hoveredModel === 'model3' || hoveredModel === null) && (
                              <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-white" />
                            )}
                          </div>
                        </th>
                      )}
                      {specifications[0].model4 && (
                        <th className={`py-3 px-2 text-center ${hoveredModel === 'model4' || hoveredModel === null ? 'opacity-100' : 'opacity-70'} transition-opacity duration-300`} style={{ minWidth: '90px' }}>
                          <div className="font-bold text-white text-xs relative">
                            {specifications[0].model4}
                            {(hoveredModel === 'model4' || hoveredModel === null) && (
                              <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-white" />
                            )}
                          </div>
                        </th>
                      )}
                      {specifications[0].model5 && (
                        <th className={`py-3 px-2 text-center ${hoveredModel === 'model5' || hoveredModel === null ? 'opacity-100' : 'opacity-70'} transition-opacity duration-300`} style={{ minWidth: '90px' }}>
                          <div className="font-bold text-white text-xs relative">
                            {specifications[0].model5}
                            {(hoveredModel === 'model5' || hoveredModel === null) && (
                              <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-white" />
                            )}
                          </div>
                        </th>
                      )}
                    </tr>
                  </thead>
                <tbody>
                    {specifications.slice(1).map((spec, index) => {
                      const isHeader = spec.category.includes('INPUT') ||
                                       spec.category.includes('OUTPUT') ||
                                       spec.category.includes('BATTERY') ||
                                       spec.category.includes('DISPLAY') ||
                                       spec.category.includes('OTHERS');

                      return (
                        <tr
                          key={index}
                          className={`border-b ${isHeader ? 'border-gray-200' : 'border-gray-100'} hover:bg-gray-50/30 transition-colors duration-200`}
                        >
                          <td className={`sticky-cell-mobile py-2 px-3 ${
                            isHeader
                              ? 'bg-gradient-to-r from-gray-100 to-gray-50'
                              : index % 2 === 0 ? 'bg-white' : 'bg-gray-50'
                          }`}>
                            <div className={`${
                              isHeader
                                ? 'font-bold text-black text-xs sm:text-sm md:text-base'
                                : 'font-medium text-black text-xs sm:text-sm md:text-base'
                            } ${isHeader ? 'pl-0' : 'pl-2 sm:pl-3 md:pl-4'}`}>
                              {isHeader ? (
                                <div className="flex items-center">
                                  <div className="w-1 h-4 bg-black rounded-full mr-2"></div>
                                  <span className="text-black font-bold">{spec.category}</span>
                                </div>
                              ) : (
                                <span className="text-black">{spec.category}</span>
                              )}
                            </div>
                          </td>
                          {spec.model1 !== undefined && (
                            <td className={`py-2 px-2 text-center text-xs sm:text-sm ${
                              isHeader
                                ? 'bg-gradient-to-r from-gray-100 to-gray-50 font-bold text-black'
                                : index % 2 === 0 ? 'bg-white text-black' : 'bg-gray-50 text-black'
                            } ${hoveredModel === 'model1' || hoveredModel === null ? 'opacity-100' : 'opacity-70'} transition-opacity duration-300`}>
                              <div className="font-medium">{spec.model1}</div>
                            </td>
                          )}
                          {spec.model2 !== undefined && (
                            <td className={`py-2 px-2 text-center text-xs sm:text-sm ${
                              isHeader
                                ? 'bg-gradient-to-r from-gray-100 to-gray-50 font-bold text-black'
                                : index % 2 === 0 ? 'bg-white text-black' : 'bg-gray-50 text-black'
                            } ${hoveredModel === 'model2' || hoveredModel === null ? 'opacity-100' : 'opacity-70'} transition-opacity duration-300`}>
                              <div className="font-medium">{spec.model2}</div>
                            </td>
                          )}
                          {spec.model3 !== undefined && (
                            <td className={`py-2 px-2 text-center text-xs sm:text-sm ${
                              isHeader
                                ? 'bg-gradient-to-r from-gray-100 to-gray-50 font-bold text-black'
                                : index % 2 === 0 ? 'bg-white text-black' : 'bg-gray-50 text-black'
                            } ${hoveredModel === 'model3' || hoveredModel === null ? 'opacity-100' : 'opacity-70'} transition-opacity duration-300`}>
                              <div className="font-medium">{spec.model3}</div>
                            </td>
                          )}
                          {spec.model4 !== undefined && (
                            <td className={`py-2 px-2 text-center text-xs sm:text-sm ${
                              isHeader
                                ? 'bg-gradient-to-r from-gray-100 to-gray-50 font-bold text-black'
                                : index % 2 === 0 ? 'bg-white text-black' : 'bg-gray-50 text-black'
                            } ${hoveredModel === 'model4' || hoveredModel === null ? 'opacity-100' : 'opacity-70'} transition-opacity duration-300`}>
                              <div className="font-medium">{spec.model4}</div>
                            </td>
                          )}
                          {spec.model5 !== undefined && (
                            <td className={`py-2 px-2 text-center text-xs sm:text-sm ${
                              isHeader
                                ? 'bg-gradient-to-r from-gray-100 to-gray-50 font-bold text-black'
                                : index % 2 === 0 ? 'bg-white text-black' : 'bg-gray-50 text-black'
                            } ${hoveredModel === 'model5' || hoveredModel === null ? 'opacity-100' : 'opacity-70'} transition-opacity duration-300`}>
                              <div className="font-medium">{spec.model5}</div>
                            </td>
                          )}
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </motion.div>
      </section>

      {/* Key Features Highlight Section */}
      <section className="container mx-auto px-4 mb-16">
        <motion.div
          className="text-center mb-10"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <h2 className="text-3xl font-bold text-black mb-3">Key Highlights</h2>
          <div className="h-1.5 w-32 bg-black mx-auto rounded-full"></div>
          <p className="mt-4 text-lg text-black max-w-2xl mx-auto">
            Standout features that make the EH 33 Series exceptional
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <motion.div
            className="bg-white p-8 rounded-xl shadow-lg border border-blue-100 relative overflow-hidden"
            whileHover={{ y: -5, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <div className="absolute top-0 left-0 w-full h-1.5 bg-black"></div>

            <h3 className="text-xl font-bold text-black mb-4">Three-Phase Reliability</h3>
            <p className="text-black mb-4">
              The EH 33 Series provides true three-phase in, three-phase out power protection with full isolation, ensuring critical infrastructure remains operational even during severe power disturbances.
            </p>

            <div className="flex items-center justify-between bg-gray-50 p-4 rounded-lg">
              <div className="text-sm text-black">
                <div className="font-bold">Power Rating</div>
                <div>80 kVA to 200 kVA models</div>
                <div>Unity power factor (kVA=kW)</div>
              </div>
              <div className="bg-gray-200 p-2 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-black" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="bg-white p-8 rounded-xl shadow-lg border border-blue-100 relative overflow-hidden"
            whileHover={{ y: -5, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <div className="absolute top-0 left-0 w-full h-1.5 bg-black"></div>

            <h3 className="text-xl font-bold text-black mb-4">Parallel Capability</h3>
            <p className="text-black mb-4">
              Connect up to six EH 33 units in parallel for increased capacity or redundancy, providing scalable power solutions that grow with your business needs while ensuring N+1 reliability.
            </p>

            <div className="flex items-center justify-between bg-gray-50 p-4 rounded-lg">
              <div className="text-sm text-black">
                <div className="font-bold">Parallel Configuration</div>
                <div>Up to 6 units in parallel</div>
                <div>Redundant communication</div>
              </div>
              <div className="bg-gray-200 p-2 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-black" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
                </svg>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="bg-white p-8 rounded-xl shadow-lg border border-blue-100 relative overflow-hidden"
            whileHover={{ y: -5, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="absolute top-0 left-0 w-full h-1.5 bg-black"></div>

            <h3 className="text-xl font-bold text-black mb-4">High Efficiency Operation</h3>
            <p className="text-black mb-4">
              With efficiency ratings of ≥ 95% in online mode and up to 98% in ECO mode, the EH 33 Series significantly reduces energy consumption and operating costs while maintaining superior protection.
            </p>

            <div className="flex items-center justify-between bg-gray-50 p-4 rounded-lg">
              <div className="text-sm text-black">
                <div className="font-bold">Efficiency</div>
                <div>≥ 95% in Online mode</div>
                <div>Up to 98% in ECO mode</div>
              </div>
              <div className="bg-gray-200 p-2 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-black" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="bg-white p-8 rounded-xl shadow-lg border border-blue-100 relative overflow-hidden"
            whileHover={{ y: -5, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <div className="absolute top-0 left-0 w-full h-1.5 bg-black"></div>

            <h3 className="text-xl font-bold text-black mb-4">Advanced Battery Management</h3>
            <p className="text-black mb-4">
              Sophisticated battery management systems extend battery life through intelligent charging, automatic testing, and deep discharge protection, maximizing your investment in backup power.
            </p>

            <div className="flex items-center justify-between bg-gray-50 p-4 rounded-lg">
              <div className="text-sm text-black">
                <div className="font-bold">Battery Configuration</div>
                <div>40 (20+20) 12V batteries</div>
                <div>Advanced monitoring and testing</div>
              </div>
              <div className="bg-gray-200 p-2 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-black" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Application Areas */}
      <section className="container mx-auto px-4 mb-16">
        <motion.div
          className="text-center mb-10"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl font-bold text-black mb-3">Ideal Applications</h2>
          <div className="h-1.5 w-24 bg-black mx-auto rounded-full"></div>
          <p className="mt-4 text-lg text-black max-w-2xl mx-auto">
            Perfect solutions for these critical environments
          </p>
        </motion.div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {[
            { icon: "🏢", text: "Data Centers", desc: "Provides mission-critical power protection for server rooms, network equipment, and data storage systems." },
            { icon: "🏥", text: "Healthcare", desc: "Delivers clean, reliable power for medical imaging equipment, patient monitoring systems, and critical healthcare infrastructure." },
            { icon: "🏭", text: "Industrial Facilities", desc: "Ensures continuous operation of manufacturing equipment, control systems, and automation processes." },
            { icon: "🔌", text: "Telecom Infrastructure", desc: "Maintains uninterrupted power for communication networks, cell towers, and network operation centers." }
          ].map((item, idx) => (
            <motion.div
              key={idx}
              className="bg-blue-50 rounded-xl p-5 shadow-sm hover:shadow-md transition-all duration-300 hover:bg-blue-100/50"
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: idx * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
            >
              <motion.div
                className="text-3xl mb-3"
                animate={{
                  y: [0, -5, 0],
                  rotate: [-2, 2, -2]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              >
                {item.icon}
              </motion.div>
              <h3 className="text-xl font-bold text-black mb-2">{item.text}</h3>
              <p className="text-black text-sm">{item.desc}</p>
            </motion.div>
          ))}
        </div>

        {/* Additional Application Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-10">
          <motion.div
            className="bg-white p-8 rounded-xl shadow-lg border border-blue-100 relative overflow-hidden"
            whileHover={{ y: -5, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-blue-400 to-blue-600"></div>
            <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-black" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-black mb-4">Data Centers</h3>
            <p className="text-black">
              Provides mission-critical power protection for server rooms, network equipment, and data storage systems with redundant configurations.
            </p>
          </motion.div>

          <motion.div
            className="bg-white p-8 rounded-xl shadow-lg border border-blue-100 relative overflow-hidden"
            whileHover={{ y: -5, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-blue-400 to-blue-600"></div>
            <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-black" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-black mb-4">Industrial Facilities</h3>
            <p className="text-black">
              Ensures continuous operation of manufacturing equipment, control systems, and automation processes in industrial environments.
            </p>
          </motion.div>

          <motion.div
            className="bg-white p-8 rounded-xl shadow-lg border border-blue-100 relative overflow-hidden"
            whileHover={{ y: -5, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-blue-400 to-blue-600"></div>
            <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-black" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-black mb-4">Healthcare</h3>
            <p className="text-black">
              Delivers clean, reliable power for medical imaging equipment, patient monitoring systems, and critical healthcare infrastructure.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Why Choose Section */}
      <section className="container mx-auto px-4 mb-16">
        <motion.div
          className="text-center mb-10"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl font-bold text-black mb-3">Why Choose EH 33 Series</h2>
          <div className="h-1.5 w-32 bg-black mx-auto rounded-full"></div>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <motion.div
            className="bg-white p-8 rounded-xl shadow-lg border border-blue-100"
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h3 className="text-xl font-bold text-black mb-6 flex items-center">
              <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center mr-4">
                <Shield className="h-5 w-5 text-black" />
              </div>
              Technical Advantages
            </h3>

            <ul className="space-y-4">
              {[
                "Unity power factor (1.0) provides full rated power capacity",
                "Dual feed capability for increased redundancy options",
                "Wide input voltage range (304-478 VAC) handles unstable power",
                "Front access maintenance simplifies service operations",
                "Advanced DSP technology for superior control and performance"
              ].map((item, index) => (
                <li key={index} className="flex items-start">
                  <div className="mt-1 mr-3 text-black bg-gray-200 p-1 rounded-full">
                    <Check size={16} />
                  </div>
                  <span className="text-black">{item}</span>
                </li>
              ))}
            </ul>
          </motion.div>

          <motion.div
            className="bg-white p-8 rounded-xl shadow-lg border border-blue-100"
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h3 className="text-xl font-bold text-black mb-6 flex items-center">
              <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center mr-4">
                <BarChart3 className="h-5 w-5 text-black" />
              </div>
              Business Benefits
            </h3>

            <ul className="space-y-4">
              {[
                "Lower total cost of ownership through high efficiency operation (≥ 95%)",
                "Enhanced power quality with < 1% harmonic distortion (THDv)",
                "Reduced energy consumption and heat output saves cooling costs",
                "Parallel capability allows for scalable system expansion",
                "Compact design saves valuable floor space in data centers"
              ].map((item, index) => (
                <li key={index} className="flex items-start">
                  <div className="mt-1 mr-3 text-black bg-gray-200 p-1 rounded-full">
                    <Check size={16} />
                  </div>
                  <span className="text-black">{item}</span>
                </li>
              ))}
            </ul>
          </motion.div>
        </div>
      </section>

      {/* Installation and Setup */}
      <section className="container mx-auto px-4 mb-16 bg-gradient-to-br from-blue-50 to-white rounded-3xl py-16">
        <motion.div
          className="text-center mb-10"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl font-bold text-black mb-3">Installation Considerations</h2>
          <div className="h-1.5 w-32 bg-black mx-auto rounded-full"></div>
          <p className="mt-4 text-lg text-black max-w-2xl mx-auto">
            Key factors to ensure optimal UPS performance
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <motion.div
            className="bg-white p-8 rounded-xl shadow-lg border border-blue-100 relative overflow-hidden"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            whileHover={{ y: -5, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.1)" }}
          >
            <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-blue-400 to-blue-600"></div>

            <div className="flex flex-col items-center text-center">
              <div className="w-14 h-14 bg-black rounded-full flex items-center justify-center text-white mb-6 font-bold text-xl shadow-md">1</div>
              <h3 className="text-xl font-bold text-black mb-4">Environment</h3>
              <p className="text-black">
                Install in a clean, temperature-controlled environment (0°C ~ 40°C) with proper ventilation and at least 1m clearance on all sides.
              </p>
            </div>
          </motion.div>

          <motion.div
            className="bg-white p-8 rounded-xl shadow-lg border border-blue-100 relative overflow-hidden"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
            whileHover={{ y: -5, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.1)" }}
          >
            <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-blue-400 to-blue-600"></div>

            <div className="flex flex-col items-center text-center">
              <div className="w-14 h-14 bg-black rounded-full flex items-center justify-center text-white mb-6 font-bold text-xl shadow-md">2</div>
              <h3 className="text-xl font-bold text-black mb-4">Power Planning</h3>
              <p className="text-black">
                Ensure input power capacity and circuit breakers are properly sized for the UPS rating, with appropriate grounding for safety.
              </p>
            </div>
          </motion.div>

          <motion.div
            className="bg-white p-8 rounded-xl shadow-lg border border-blue-100 relative overflow-hidden"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            whileHover={{ y: -5, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.1)" }}
          >
            <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-blue-400 to-blue-600"></div>

            <div className="flex flex-col items-center text-center">
              <div className="w-14 h-14 bg-black rounded-full flex items-center justify-center text-white mb-6 font-bold text-xl shadow-md">3</div>
              <h3 className="text-xl font-bold text-black mb-4">Battery Setup</h3>
              <p className="text-black">
                Properly configure the 40-piece battery set with correct polarity and connections, ensuring battery cabinet ventilation meets requirements.
              </p>
            </div>
          </motion.div>
        </div>
      </section>



      {/* Need More Information Section */}
      <section className="container mx-auto px-4 mb-20">
        <motion.div
          className="bg-blue-50 rounded-xl overflow-hidden shadow-md"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <div className="py-10 px-6 text-center">
            <motion.h2
              className="text-2xl md:text-3xl font-bold mb-4 text-black"
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.1 }}
              viewport={{ once: true }}
            >
              Need More Information?
            </motion.h2>

            <motion.p
              className="text-black mb-8 max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.2 }}
              viewport={{ once: true }}
            >
              Our team of experts is ready to help you with product specifications, custom solutions, pricing, and
              any other details you need about the KRYKARD UPS systems.
            </motion.p>

            <motion.div
              className="flex justify-center"
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <motion.a
                href="/contact/sales"
                className="bg-blue-600 text-white hover:bg-blue-700 shadow-md transition-all duration-300 px-6 py-3 rounded-md font-medium flex items-center justify-center gap-2"
                whileHover={{ scale: 1.03, boxShadow: "0 10px 15px -3px rgba(59, 130, 246, 0.3)" }}
                whileTap={{ scale: 0.98 }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                </svg>
                <span>Contact Our Experts</span>
              </motion.a>
            </motion.div>
          </div>
        </motion.div>
      </section>



      {/* PDF Viewer Modal */}
      <PdfViewer />
    </div>
  );

  // Return PageLayout component with the product specification content inside
  return (
    <PageLayout
      title="KRYKARD EH 33 Large Series Three Phase UPS"
      subtitle="The high-frequency transformer-less static converter UPS"
      category="protect"
      image="/background_images/ups_layout.png"
    >
      <ProductSpecContent />
    </PageLayout>
  );
};

export default ProductSpecification;